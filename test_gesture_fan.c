/**
 * STM32F103VET6 手势识别风扇控制系统测试程序
 * 
 * 本测试程序用于验证从STM32F407ZGT6移植到STM32F103VET6的功能是否正常
 * 
 * 测试内容：
 * 1. 硬件模块初始化测试
 * 2. 光电开关状态读取测试
 * 3. 超声波测距功能测试
 * 4. DAC输出测试
 * 5. 风扇控制功能测试
 * 6. 手势识别功能测试
 * 7. 距离稳定性检测测试
 * 8. 查找表功能测试
 */

#include "stm32f1xx_hal.h"
#include "usart.h"
#include "gpio.h"

// 硬件模块
#include "HARDWARE/ULTRASONIC/ultrasonic.h"
#include "HARDWARE/PHOTOSWITCH/photoswitch.h"
#include "HARDWARE/FAN/fan.h"
#include "HARDWARE/DAC/dac.h"

// 应用模块
#include "APP/GESTURE/gesture.h"
#include "APP/INFO/info.h"

// 测试函数声明
void Test_Hardware_Init(void);
void Test_PhotoSwitch(void);
void Test_Ultrasonic(void);
void Test_DAC(void);
void Test_Fan_Control(void);
void Test_Gesture_Recognition(void);
void Test_Distance_Stability(void);
void Test_Lookup_Tables(void);

/**
 * @brief  测试主函数
 */
void Run_All_Tests(void)
{
    printf("\n\r=== STM32F103VET6 手势识别风扇控制系统测试开始 ===\n\r");
    
    Test_Hardware_Init();
    HAL_Delay(1000);
    
    Test_PhotoSwitch();
    HAL_Delay(1000);
    
    Test_Ultrasonic();
    HAL_Delay(1000);
    
    Test_DAC();
    HAL_Delay(1000);
    
    Test_Fan_Control();
    HAL_Delay(1000);
    
    Test_Gesture_Recognition();
    HAL_Delay(1000);
    
    Test_Distance_Stability();
    HAL_Delay(1000);
    
    Test_Lookup_Tables();
    
    printf("\n\r=== 所有测试完成 ===\n\r");
}

/**
 * @brief  硬件初始化测试
 */
void Test_Hardware_Init(void)
{
    printf("\n\r--- 测试1: 硬件模块初始化 ---\n\r");
    
    printf("初始化超声波模块...");
    Ultrasonic_Init();
    printf("完成\n\r");
    
    printf("初始化光电开关模块...");
    PhotoSwitch_Init();
    printf("完成\n\r");
    
    printf("初始化DAC模块...");
    DAC_Init();
    printf("完成\n\r");
    
    printf("初始化风扇控制模块...");
    Fan_Init();
    printf("完成\n\r");
    
    printf("初始化手势识别模块...");
    Gesture_Init();
    printf("完成\n\r");
    
    printf("硬件初始化测试通过\n\r");
}

/**
 * @brief  光电开关测试
 */
void Test_PhotoSwitch(void)
{
    printf("\n\r--- 测试2: 光电开关状态读取 ---\n\r");
    
    for(int i = 0; i < 10; i++)
    {
        PhotoSwitch_Update();
        
        printf("S1:%d S2:%d S3:%d S4:%d\n\r",
               PhotoSwitch_IsTriggered(SWITCH_S1),
               PhotoSwitch_IsTriggered(SWITCH_S2),
               PhotoSwitch_IsTriggered(SWITCH_S3),
               PhotoSwitch_IsTriggered(SWITCH_S4));
        
        HAL_Delay(200);
    }
    
    printf("光电开关测试完成\n\r");
}

/**
 * @brief  超声波测距测试
 */
void Test_Ultrasonic(void)
{
    printf("\n\r--- 测试3: 超声波测距功能 ---\n\r");
    
    for(int i = 0; i < 10; i++)
    {
        float distance = Ultrasonic_GetDistance();
        printf("距离: %.2f cm\n\r", distance);
        HAL_Delay(500);
    }
    
    printf("超声波测距测试完成\n\r");
}

/**
 * @brief  DAC输出测试
 */
void Test_DAC(void)
{
    printf("\n\r--- 测试4: DAC输出测试 ---\n\r");
    
    uint16_t test_values[] = {0, 1024, 2048, 3072, 4095};
    
    for(int i = 0; i < 5; i++)
    {
        DAC_SetValue(test_values[i]);
        float voltage = DAC_GetVoltage();
        printf("DAC值: %d, 输出电压: %.3f V\n\r", test_values[i], voltage);
        HAL_Delay(1000);
    }
    
    // 恢复到0
    DAC_SetValue(0);
    printf("DAC测试完成\n\r");
}

/**
 * @brief  风扇控制测试
 */
void Test_Fan_Control(void)
{
    printf("\n\r--- 测试5: 风扇控制功能 ---\n\r");
    
    printf("测试风扇正转启动...\n\r");
    Fan_StartForward();
    HAL_Delay(2000);
    
    printf("测试风扇速度调节...\n\r");
    Fan_SetSpeed(2048);  // 50%速度
    HAL_Delay(2000);
    
    printf("测试风扇反转...\n\r");
    Fan_StartReverse();
    HAL_Delay(2000);
    
    printf("测试风扇停止...\n\r");
    Fan_Stop();
    HAL_Delay(1000);
    
    printf("风扇控制测试完成\n\r");
}

/**
 * @brief  手势识别测试
 */
void Test_Gesture_Recognition(void)
{
    printf("\n\r--- 测试6: 手势识别功能 ---\n\r");
    printf("请在10秒内进行手势操作...\n\r");
    
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 10000)  // 10秒测试
    {
        PhotoSwitch_Update();
        GestureType_t gesture = Gesture_Recognize();
        
        if(gesture != GESTURE_NONE)
        {
            printf("识别到手势: %s\n\r", Gesture_GetName(gesture));
        }
        
        HAL_Delay(10);
    }
    
    printf("手势识别测试完成\n\r");
}

/**
 * @brief  距离稳定性检测测试
 */
void Test_Distance_Stability(void)
{
    printf("\n\r--- 测试7: 距离稳定性检测 ---\n\r");
    printf("请保持物体在传感器前稳定3秒...\n\r");
    
    uint32_t start_time = HAL_GetTick();
    uint16_t last_stable = 0;
    uint8_t stability_detected = 0;
    
    while(HAL_GetTick() - start_time < 15000 && !stability_detected)  // 15秒测试
    {
        float distance = Ultrasonic_GetDistance();
        uint16_t rounded = (uint16_t)(distance + 0.5f);
        
        if(rounded != last_stable)
        {
            last_stable = rounded;
            printf("距离变化: %d cm\n\r", rounded);
        }
        
        // 简化的稳定性检测逻辑
        static uint16_t stable_count = 0;
        static uint16_t last_distance = 0;
        
        if(rounded == last_distance)
        {
            stable_count++;
            if(stable_count >= 200)  // 2秒稳定 (200 * 10ms)
            {
                printf("检测到稳定距离: %d cm\n\r", rounded);
                stability_detected = 1;
            }
        }
        else
        {
            stable_count = 0;
            last_distance = rounded;
        }
        
        HAL_Delay(10);
    }
    
    printf("距离稳定性检测测试完成\n\r");
}

/**
 * @brief  查找表功能测试
 */
void Test_Lookup_Tables(void)
{
    printf("\n\r--- 测试8: 查找表功能 ---\n\r");
    
    uint16_t test_distances[] = {5, 10, 15, 20, 25, 30};
    
    printf("距离-时间查找表测试:\n\r");
    for(int i = 0; i < 6; i++)
    {
        // 这里需要实现查找表函数，暂时用示例值
        printf("距离%dcm -> 时间%d秒\n\r", test_distances[i], test_distances[i] * 2);
    }
    
    printf("\n\r距离-电压查找表测试:\n\r");
    for(int i = 0; i < 6; i++)
    {
        // 这里需要实现查找表函数，暂时用示例值
        printf("距离%dcm -> 电压%.1fV\n\r", test_distances[i], test_distances[i] * 0.3f + 2.0f);
    }
    
    printf("查找表功能测试完成\n\r");
}
