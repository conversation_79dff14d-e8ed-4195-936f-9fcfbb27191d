#include "dac.h"

//////////////////////////////////////////////////////////////////////////////////	 
// STM32F407 DAC输出模块实现
// 使用DAC1_CH1输出模拟电压
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
DAC_HandleTypeDef hdac;
DAC_ChannelConfTypeDef sConfig;

/**
 * @brief  DAC模块初始化
 * @param  None
 * @retval None
 */
void DAC_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_DAC_CLK_ENABLE();
    
    // 配置GPIO为模拟模式
    GPIO_InitStruct.Pin = DAC_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(DAC_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置DAC
    hdac.Instance = DAC;
    if (HAL_DAC_Init(&hdac) != HAL_OK)
    {
        // 初始化错误处理
        while(1);
    }
    
    // 配置DAC通道
    sConfig.DAC_Trigger = DAC_TRIGGER_NONE;
    sConfig.DAC_OutputBuffer = DAC_OUTPUTBUFFER_ENABLE;
    
    if (HAL_DAC_ConfigChannel(&hdac, &sConfig, DAC_CHANNEL) != HAL_OK)
    {
        // 配置错误处理
        while(1);
    }
    
    // 启动DAC
    HAL_DAC_Start(&hdac, DAC_CHANNEL);
    
    // 初始输出0V
    DAC_SetValue(0);
}

/**
 * @brief  设置DAC数值
 * @param  value: DAC数值 (0-4095)
 * @retval None
 */
void DAC_SetValue(uint16_t value)
{
    if(value > DAC_MAX_VALUE) value = DAC_MAX_VALUE;
    
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL, DAC_ALIGN_12B_R, value);
}

/**
 * @brief  设置DAC电压
 * @param  voltage: 电压值 (0-3.3V)
 * @retval None
 */
void DAC_SetVoltage(float voltage)
{
    uint16_t dac_value;
    
    if(voltage < 0) voltage = 0;
    if(voltage > DAC_VREF) voltage = DAC_VREF;
    
    // 电压转换为DAC数值
    dac_value = (uint16_t)(voltage * DAC_MAX_VALUE / DAC_VREF);
    
    DAC_SetValue(dac_value);
}

/**
 * @brief  获取当前DAC数值
 * @param  None
 * @retval 当前DAC数值
 */
uint16_t DAC_GetValue(void)
{
    return HAL_DAC_GetValue(&hdac, DAC_CHANNEL);
}

/**
 * @brief  获取当前DAC电压
 * @param  None
 * @retval 当前DAC电压
 */
float DAC_GetVoltage(void)
{
    uint16_t dac_value = DAC_GetValue();
    return (float)dac_value * DAC_VREF / DAC_MAX_VALUE;
}
