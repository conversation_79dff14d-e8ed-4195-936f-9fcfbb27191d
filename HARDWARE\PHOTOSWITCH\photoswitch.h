#ifndef __PHOTOSWITCH_H
#define __PHOTOSWITCH_H
#include "stm32f1xx_hal.h"

//////////////////////////////////////////////////////////////////////////////////
// 非接触式控制盘 - 光电开关模块 (STM32F103VET6版本)
//
// 【硬件说明】
// 使用4个漫反射式光电开关(S1-S4)，布置在控制面板的四个位置
// 开关类型：漫反射式，检测距离约5-15cm
// 输出特性：低电平有效(手掌接近时输出低电平)
//
// 【工作原理】
// 1. 光电开关发射红外光，当手掌接近时反射光被接收器检测
// 2. 接收到反射光时输出低电平(SWITCH_TRIGGERED)
// 3. 无反射光时输出高电平(SWITCH_RELEASED)
// 4. 通过检测电平变化的时序，识别挥手方向和速度
//
// 【布局建议】
// S1 S2    (上排：左右控制)
// S3 S4    (下排：上下控制)
//
// 【手势检测】
// 横向手势：S1→S2(正转启动), S2→S1(正转停止)
// 纵向手势：S3→S4(反转启动), S4→S3(反转停止)
// 对角手势：S4→S2(电压上升), S3→S1(电压下降)
// 设定手势：S3→S2(时间设定), S4→S1(电压设定)
//////////////////////////////////////////////////////////////////////////////////

// 光电开关引脚定义 - 保持与F407相同的PC0-PC3
#define S1_GPIO_PORT    GPIOC
#define S1_GPIO_PIN     GPIO_PIN_0
#define S2_GPIO_PORT    GPIOC
#define S2_GPIO_PIN     GPIO_PIN_1
#define S3_GPIO_PORT    GPIOC
#define S3_GPIO_PIN     GPIO_PIN_2
#define S4_GPIO_PORT    GPIOC
#define S4_GPIO_PIN     GPIO_PIN_3

// 光电开关读取宏定义
#define S1_READ()       HAL_GPIO_ReadPin(S1_GPIO_PORT, S1_GPIO_PIN)
#define S2_READ()       HAL_GPIO_ReadPin(S2_GPIO_PORT, S2_GPIO_PIN)
#define S3_READ()       HAL_GPIO_ReadPin(S3_GPIO_PORT, S3_GPIO_PIN)
#define S4_READ()       HAL_GPIO_ReadPin(S4_GPIO_PORT, S4_GPIO_PIN)

// 开关状态定义
#define SWITCH_TRIGGERED    GPIO_PIN_RESET  // 低电平触发
#define SWITCH_RELEASED     GPIO_PIN_SET    // 高电平释放

// 开关编号枚举
typedef enum {
    SWITCH_S1 = 0,
    SWITCH_S2 = 1,
    SWITCH_S3 = 2,
    SWITCH_S4 = 3,
    SWITCH_COUNT = 4
} PhotoSwitch_t;

// 开关状态结构体
typedef struct {
    GPIO_PinState current_state[SWITCH_COUNT];  // 当前状态
    GPIO_PinState last_state[SWITCH_COUNT];     // 上次状态
    uint32_t trigger_time[SWITCH_COUNT];        // 触发时间
    uint8_t changed_flag[SWITCH_COUNT];         // 状态变化标志
} PhotoSwitchStatus_t;

// 全局变量声明
extern PhotoSwitchStatus_t switch_status;

// 函数声明
void PhotoSwitch_Init(void);                    // 光电开关初始化
void PhotoSwitch_Scan(void);                    // 扫描开关状态
#define PhotoSwitch_Update() PhotoSwitch_Scan() // 兼容性别名
uint8_t PhotoSwitch_IsTriggered(PhotoSwitch_t sw);     // 检查是否触发
uint8_t PhotoSwitch_IsReleased(PhotoSwitch_t sw);      // 检查是否释放
uint8_t PhotoSwitch_IsChanged(PhotoSwitch_t sw);       // 检查状态是否改变
uint32_t PhotoSwitch_GetTriggerTime(PhotoSwitch_t sw); // 获取触发时间
void PhotoSwitch_ClearFlags(void);             // 清除状态标志

#endif
