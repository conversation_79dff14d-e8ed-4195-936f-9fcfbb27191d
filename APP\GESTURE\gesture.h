#ifndef __GESTURE_H
#define __GESTURE_H
#include "stm32f1xx_hal.h"
#include "../../HARDWARE/PHOTOSWITCH/photoswitch.h"

//////////////////////////////////////////////////////////////////////////////////
// 非接触式控制盘 - 手势识别模块 (STM32F103VET6版本)
//
// 【功能说明】
// 通过4个光电开关检测挥手手势，实现非接触式控制
// 支持8种手势组合识别，每种手势对应不同的控制功能
//
// 【识别原理】
// 1. 连续监测4个光电开关的状态变化
// 2. 记录每个开关的触发时间戳
// 3. 分析两个开关的触发顺序和时间间隔
// 4. 根据预定义的手势模式进行匹配识别
//
// 【时序要求】
// - 手势超时时间：1000ms（防止误触发）
// - 有效时间间隔：50-800ms（过快或过慢都无效）
// - 开关触发检测：低电平有效
//
// 【支持的手势】
// S1→S2: 风扇正转启动    S2→S1: 风扇正转停止
// S3→S4: 风扇反转启动    S4→S3: 风扇反转停止
// S4→S2: 电压上升        S3→S1: 电压下降
// S3→S2: 时间设定        S4→S1: 电压设定
//////////////////////////////////////////////////////////////////////////////////

// 手势类型枚举
typedef enum {
    GESTURE_NONE = 0,       // 无手势
    GESTURE_S1_TO_S2,       // S1→S2 (风扇正转启动)
    GESTURE_S2_TO_S1,       // S2→S1 (风扇正转停止)
    GESTURE_S3_TO_S4,       // S3→S4 (风扇反转启动)
    GESTURE_S4_TO_S3,       // S4→S3 (风扇反转停止)
    GESTURE_S1_TO_S4,       // S1→S4 (电压上升)
    GESTURE_S4_TO_S1,       // S4→S1 (电压下降)
    GESTURE_S4_TO_S2,       // S4→S2 (备用)
    GESTURE_S3_TO_S1,       // S3→S1 (备用)
    GESTURE_S3_TO_S2,       // S3→S2 (时间设定)
    GESTURE_COUNT
} GestureType_t;

// 手势识别参数
#define GESTURE_TIMEOUT_MS      1000    // 手势超时时间
#define GESTURE_MIN_INTERVAL    50      // 最小间隔时间
#define GESTURE_MAX_INTERVAL    800     // 最大间隔时间

// 手势状态结构体
typedef struct {
    PhotoSwitch_t first_switch;     // 第一个触发的开关
    PhotoSwitch_t second_switch;    // 第二个触发的开关
    uint32_t first_time;            // 第一个开关触发时间
    uint32_t second_time;           // 第二个开关触发时间
    uint8_t gesture_active;         // 手势识别激活标志
    GestureType_t last_gesture;     // 上次识别的手势
} GestureStatus_t;

// 全局变量声明
extern GestureStatus_t gesture_status;

// 滑动逻辑检测结果枚举
typedef enum {
    SLIDE_RESULT_NONE = 0,      // 无滑动
    SLIDE_RESULT_VALID,         // 有效滑动
    SLIDE_RESULT_TOO_FAST,      // 滑动过快（<50ms）
    SLIDE_RESULT_TOO_SLOW,      // 滑动过慢（>800ms）
    SLIDE_RESULT_TIMEOUT        // 超时（>1000ms）
} SlideResult_t;

// 滑动检测状态结构体
typedef struct {
    SlideResult_t last_result;  // 上次检测结果
    uint32_t invalid_count;     // 无效操作计数
    uint32_t last_invalid_time; // 上次无效操作时间
} SlideDetection_t;

// 函数声明
void Gesture_Init(void);                        // 手势识别初始化
GestureType_t Gesture_Recognize(void);          // 手势识别
void Gesture_Reset(void);                       // 重置手势状态
const char* Gesture_GetName(GestureType_t gesture); // 获取手势名称
uint8_t Gesture_IsValid(GestureType_t gesture); // 检查手势有效性
SlideResult_t Gesture_CheckSlideLogic(void);    // 滑动逻辑检测
void Gesture_ClearInvalidOperation(void);       // 清除无效操作标志

#endif
