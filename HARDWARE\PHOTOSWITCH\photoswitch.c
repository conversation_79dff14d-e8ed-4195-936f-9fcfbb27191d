#include "photoswitch.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 光电开关模块实现 (STM32F103VET6版本)
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
PhotoSwitchStatus_t switch_status;
int i = 0;

/**
 * @brief  光电开关模块初始化
 * @param  None
 * @retval None
 */
void PhotoSwitch_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    __HAL_RCC_GPIOC_CLK_ENABLE();
    
    // 配置S1引脚
    GPIO_InitStruct.Pin = S1_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // 上拉
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(S1_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置S2引脚
    GPIO_InitStruct.Pin = S2_GPIO_PIN;
    HAL_GPIO_Init(S2_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置S3引脚
    GPIO_InitStruct.Pin = S3_GPIO_PIN;
    HAL_GPIO_Init(S3_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置S4引脚
    GPIO_InitStruct.Pin = S4_GPIO_PIN;
    HAL_GPIO_Init(S4_GPIO_PORT, &GPIO_InitStruct);
    
    // 初始化状态结构体
    for(i = 0; i < SWITCH_COUNT; i++)
    {
        switch_status.current_state[i] = SWITCH_RELEASED;
        switch_status.last_state[i] = SWITCH_RELEASED;
        switch_status.trigger_time[i] = 0;
        switch_status.changed_flag[i] = 0;
    }
    
    // 初始读取一次状态
    PhotoSwitch_Scan();
}

/**
 * @brief  扫描光电开关状态
 * @param  None
 * @retval None
 */
void PhotoSwitch_Scan(void)
{
    GPIO_PinState new_state[SWITCH_COUNT];
    uint32_t current_time = HAL_GetTick();
    
    // 读取当前状态
    new_state[SWITCH_S1] = S1_READ();
    new_state[SWITCH_S2] = S2_READ();
    new_state[SWITCH_S3] = S3_READ();
    new_state[SWITCH_S4] = S4_READ();
    
    // 检查状态变化
    for(i = 0; i < SWITCH_COUNT; i++)
    {
        // 保存上次状态
        switch_status.last_state[i] = switch_status.current_state[i];
        switch_status.current_state[i] = new_state[i];
        
        // 检测状态变化
        if(switch_status.current_state[i] != switch_status.last_state[i])
        {
            switch_status.changed_flag[i] = 1;
            
            // 如果是触发状态，记录时间
            if(switch_status.current_state[i] == SWITCH_TRIGGERED)
            {
                switch_status.trigger_time[i] = current_time;
            }
        }
        else
        {
            switch_status.changed_flag[i] = 0;
        }
    }
}

/**
 * @brief  检查开关是否被触发
 * @param  sw: 开关编号
 * @retval 1-触发, 0-未触发
 */
uint8_t PhotoSwitch_IsTriggered(PhotoSwitch_t sw)
{
    if(sw >= SWITCH_COUNT) return 0;
    return (switch_status.current_state[sw] == SWITCH_TRIGGERED);
}

/**
 * @brief  检查开关是否被释放
 * @param  sw: 开关编号
 * @retval 1-释放, 0-未释放
 */
uint8_t PhotoSwitch_IsReleased(PhotoSwitch_t sw)
{
    if(sw >= SWITCH_COUNT) return 0;
    return (switch_status.current_state[sw] == SWITCH_RELEASED);
}

/**
 * @brief  检查开关状态是否改变
 * @param  sw: 开关编号
 * @retval 1-改变, 0-未改变
 */
uint8_t PhotoSwitch_IsChanged(PhotoSwitch_t sw)
{
    if(sw >= SWITCH_COUNT) return 0;
    return switch_status.changed_flag[sw];
}

/**
 * @brief  获取开关触发时间
 * @param  sw: 开关编号
 * @retval 触发时间(ms)
 */
uint32_t PhotoSwitch_GetTriggerTime(PhotoSwitch_t sw)
{
    if(sw >= SWITCH_COUNT) return 0;
    return switch_status.trigger_time[sw];
}

/**
 * @brief  清除所有状态标志
 * @param  None
 * @retval None
 */
void PhotoSwitch_ClearFlags(void)
{
    for(i = 0; i < SWITCH_COUNT; i++)
    {
        switch_status.changed_flag[i] = 0;
    }
}
