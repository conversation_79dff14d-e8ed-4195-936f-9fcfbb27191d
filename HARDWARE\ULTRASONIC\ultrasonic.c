#include "ultrasonic.h"

//////////////////////////////////////////////////////////////////////////////////	 
// STM32F103VET6 超声波测距模块实现
// 支持HC-SR04超声波传感器
// 适配STM32F103VET6平台
//////////////////////////////////////////////////////////////////////////////////

// 微秒延时函数 - 适配STM32F103
static void delay_us(uint32_t us)
{
    uint32_t delay = us * (SystemCoreClock / 1000000);
    while(delay--);
}

/**
 * @brief  超声波模块初始化
 * @param  None
 * @retval None
 */
void Ultrasonic_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIOB时钟 - 适配F103VET6
    __HAL_RCC_GPIOB_CLK_ENABLE();
    
    // 配置TRIG引脚为输出 (PB6)
    GPIO_InitStruct.Pin = TRIG_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(TRIG_PORT, &GPIO_InitStruct);
    
    // 配置ECHO引脚为输入 (PB7)
    GPIO_InitStruct.Pin = ECHO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(ECHO_PORT, &GPIO_InitStruct);
    
    TRIG_LOW; // 初始化TRIG为低电平
}

/**
 * @brief  发送超声波触发脉冲
 * @param  None
 * @retval None
 */
void Ultrasonic_Trigger(void)
{
    TRIG_HIGH;
    delay_us(15);  // 发送10us高电平脉冲
    TRIG_LOW;
}

/**
 * @brief  获取超声波测距结果
 * @param  None
 * @retval 距离值(cm)，-1表示测量失败
 */
float Ultrasonic_GetDistance(void)
{
    uint32_t echo_time = 0;
    uint32_t timeout = 0;
    
    // 发送触发脉冲
    Ultrasonic_Trigger();
    
    // 等待ECHO变高
    timeout = 0;
    while(ECHO_READ == GPIO_PIN_RESET && timeout < 10000)
    {
        timeout++;
        delay_us(1);
    }
    if(timeout >= 10000) return -1; // 超时
    
    // 计算ECHO高电平持续时间
    echo_time = 0;
    while(ECHO_READ == GPIO_PIN_SET && echo_time < ULTRASONIC_TIMEOUT)
    {
        echo_time++;
        delay_us(1);
    }
    
    if(echo_time >= ULTRASONIC_TIMEOUT) return -1; // 超时
    
    // 计算距离：时间(us) * 声速(346m/s) / 2
    // 由于delay_us(1)实际延时可能不是1μs，需要校正系数
    // 如果测出距离是实际的10倍，说明系数应该是0.0173 = 0.173
    return (float)echo_time * 0.173f;
}

/**
 * @brief  判断距离是否在有效测量范围内
 * @param  distance: 距离值
 * @retval 1-有效范围，0-无效范围
 */
uint8_t Ultrasonic_IsInRange(float distance)
{
    return (distance >= ULTRASONIC_MIN_DISTANCE && distance <= ULTRASONIC_MAX_DISTANCE);
}
