Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) for HAL_SYSTICK_IRQHandler
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    main.o(i.Check_Distance_Stability) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.Check_Distance_Stability) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.Check_Distance_Stability) refers to ffixui.o(.text) for __aeabi_f2uiz
    main.o(i.Check_Distance_Stability) refers to main.o(.data) for .data
    main.o(i.Get_Time_From_Distance) refers to main.o(.constdata) for .constdata
    main.o(i.Get_Voltage_From_Distance) refers to main.o(.constdata) for .constdata
    main.o(i.Process_Gesture) refers to gesture.o(i.Gesture_GetName) for Gesture_GetName
    main.o(i.Process_Gesture) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Process_Gesture) refers to fan.o(i.Fan_StartForward) for Fan_StartForward
    main.o(i.Process_Gesture) refers to fan.o(i.Fan_StartReverse) for Fan_StartReverse
    main.o(i.Process_Gesture) refers to fan.o(i.Fan_Stop) for Fan_Stop
    main.o(i.Process_Gesture) refers to fan.o(i.Fan_IsRunning) for Fan_IsRunning
    main.o(i.Process_Gesture) refers to fan.o(i.Fan_StartVoltageAdjust) for Fan_StartVoltageAdjust
    main.o(i.Process_Gesture) refers to main.o(i.Get_Time_From_Distance) for Get_Time_From_Distance
    main.o(i.Process_Gesture) refers to info.o(i.Info_SendSetTime) for Info_SendSetTime
    main.o(i.Process_Gesture) refers to main.o(i.Get_Voltage_From_Distance) for Get_Voltage_From_Distance
    main.o(i.Process_Gesture) refers to fan.o(i.Fan_SetSpeedByVoltage) for Fan_SetSpeedByVoltage
    main.o(i.Process_Gesture) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.Process_Gesture) refers to fdiv.o(.text) for __aeabi_fdiv
    main.o(i.Process_Gesture) refers to info.o(i.Info_SendSetVoltage) for Info_SendSetVoltage
    main.o(i.Process_Gesture) refers to info.o(i.Info_SendFinalDistance) for Info_SendFinalDistance
    main.o(i.Process_Gesture) refers to main.o(.data) for .data
    main.o(i.Process_Gesture) refers to main.o(.conststring) for .conststring
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    main.o(i.System_Init) refers to ultrasonic.o(i.Ultrasonic_Init) for Ultrasonic_Init
    main.o(i.System_Init) refers to photoswitch.o(i.PhotoSwitch_Init) for PhotoSwitch_Init
    main.o(i.System_Init) refers to dac.o(i.DAC_Init) for DAC_Init
    main.o(i.System_Init) refers to fan.o(i.Fan_Init) for Fan_Init
    main.o(i.System_Init) refers to gesture.o(i.Gesture_Init) for Gesture_Init
    main.o(i.System_Init) refers to info.o(i.Info_Init) for Info_Init
    main.o(i.System_Init) refers to main.o(.data) for .data
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to main.o(i.System_Init) for System_Init
    main.o(i.main) refers to photoswitch.o(i.PhotoSwitch_Scan) for PhotoSwitch_Scan
    main.o(i.main) refers to gesture.o(i.Gesture_Recognize) for Gesture_Recognize
    main.o(i.main) refers to main.o(i.Process_Gesture) for Process_Gesture
    main.o(i.main) refers to ultrasonic.o(i.Ultrasonic_GetDistance) for Ultrasonic_GetDistance
    main.o(i.main) refers to main.o(i.Check_Distance_Stability) for Check_Distance_Stability
    main.o(i.main) refers to fan.o(i.Fan_Update) for Fan_Update
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to info.o(i.Info_SendDistance) for Info_SendDistance
    main.o(i.main) refers to fan.o(i.Fan_GetVoltage) for Fan_GetVoltage
    main.o(i.main) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.main) refers to fdiv.o(.text) for __aeabi_fdiv
    main.o(i.main) refers to fan.o(i.Fan_IsRunning) for Fan_IsRunning
    main.o(i.main) refers to fan.o(i.Fan_GetDirection) for Fan_GetDirection
    main.o(i.main) refers to info.o(i.Info_SendFanInfoWithLookup) for Info_SendFanInfoWithLookup
    main.o(i.main) refers to info.o(i.Info_SendFinalDistance) for Info_SendFinalDistance
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to main.o(.data) for .data
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f1xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f1xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f1xx_hal_dac.o(i.HAL_DAC_DeInit) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f1xx_hal_dac.o(i.HAL_DAC_Init) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f1xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f1xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f1xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f1xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    gesture.o(i.Gesture_CheckSlideLogic) refers to gesture.o(.bss) for .bss
    gesture.o(i.Gesture_ClearInvalidOperation) refers to gesture.o(.bss) for .bss
    gesture.o(i.Gesture_GetName) refers to gesture.o(.data) for .data
    gesture.o(i.Gesture_Init) refers to gesture.o(.bss) for .bss
    gesture.o(i.Gesture_Recognize) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    gesture.o(i.Gesture_Recognize) refers to gesture.o(i.Gesture_Reset) for Gesture_Reset
    gesture.o(i.Gesture_Recognize) refers to photoswitch.o(i.PhotoSwitch_IsChanged) for PhotoSwitch_IsChanged
    gesture.o(i.Gesture_Recognize) refers to photoswitch.o(i.PhotoSwitch_IsTriggered) for PhotoSwitch_IsTriggered
    gesture.o(i.Gesture_Recognize) refers to photoswitch.o(i.PhotoSwitch_GetTriggerTime) for PhotoSwitch_GetTriggerTime
    gesture.o(i.Gesture_Recognize) refers to gesture.o(.bss) for .bss
    gesture.o(i.Gesture_Reset) refers to photoswitch.o(i.PhotoSwitch_ClearFlags) for PhotoSwitch_ClearFlags
    gesture.o(i.Gesture_Reset) refers to gesture.o(.bss) for .bss
    gesture.o(.data) refers to gesture.o(.conststring) for .conststring
    info.o(i.Info_GetRpmByVoltage) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    info.o(i.Info_GetRpmByVoltage) refers to cfcmple.o(.text) for __aeabi_cfcmple
    info.o(i.Info_GetRpmByVoltage) refers to fadd.o(.text) for __aeabi_fsub
    info.o(i.Info_GetRpmByVoltage) refers to fflti.o(.text) for __aeabi_i2f
    info.o(i.Info_GetRpmByVoltage) refers to fdiv.o(.text) for __aeabi_fdiv
    info.o(i.Info_GetRpmByVoltage) refers to fmul.o(.text) for __aeabi_fmul
    info.o(i.Info_GetRpmByVoltage) refers to ffixui.o(.text) for __aeabi_f2uiz
    info.o(i.Info_GetRpmByVoltage) refers to info.o(.constdata) for .constdata
    info.o(i.Info_Init) refers to memseta.o(.text) for __aeabi_memclr
    info.o(i.Info_Init) refers to info.o(i.Info_SendSetTime) for Info_SendSetTime
    info.o(i.Info_Init) refers to info.o(i.Info_SendCountdown) for Info_SendCountdown
    info.o(i.Info_Init) refers to info.o(i.Info_SendSetVoltage) for Info_SendSetVoltage
    info.o(i.Info_Init) refers to info.o(i.Info_SendDistance) for Info_SendDistance
    info.o(i.Info_Init) refers to info.o(i.Info_SendRPM) for Info_SendRPM
    info.o(i.Info_Init) refers to info.o(i.Info_SendDirection) for Info_SendDirection
    info.o(i.Info_Init) refers to info.o(i.Info_SendMotionState) for Info_SendMotionState
    info.o(i.Info_Init) refers to info.o(.bss) for .bss
    info.o(i.Info_SendCommand) refers to strlen.o(.text) for strlen
    info.o(i.Info_SendCommand) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    info.o(i.Info_SendCommand) refers to usart.o(.bss) for huart1
    info.o(i.Info_SendCountdown) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendCountdown) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendCountdown) refers to info.o(.bss) for .bss
    info.o(i.Info_SendDirection) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendDirection) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendDirection) refers to info.o(.bss) for .bss
    info.o(i.Info_SendDistance) refers to f2d.o(.text) for __aeabi_f2d
    info.o(i.Info_SendDistance) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendDistance) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendDistance) refers to info.o(.bss) for .bss
    info.o(i.Info_SendFanInfo) refers to info.o(i.Info_SendSetVoltage) for Info_SendSetVoltage
    info.o(i.Info_SendFanInfo) refers to info.o(i.Info_SendRPM) for Info_SendRPM
    info.o(i.Info_SendFanInfo) refers to info.o(i.Info_SendDirection) for Info_SendDirection
    info.o(i.Info_SendFanInfo) refers to info.o(i.Info_SendMotionState) for Info_SendMotionState
    info.o(i.Info_SendFanInfoWithLookup) refers to info.o(i.Info_SendSetVoltage) for Info_SendSetVoltage
    info.o(i.Info_SendFanInfoWithLookup) refers to info.o(i.Info_GetRpmByVoltage) for Info_GetRpmByVoltage
    info.o(i.Info_SendFanInfoWithLookup) refers to info.o(i.Info_SendRPM) for Info_SendRPM
    info.o(i.Info_SendFanInfoWithLookup) refers to info.o(i.Info_SendDirection) for Info_SendDirection
    info.o(i.Info_SendFanInfoWithLookup) refers to info.o(i.Info_SendMotionState) for Info_SendMotionState
    info.o(i.Info_SendFinalDistance) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendFinalDistance) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendFinalDistance) refers to info.o(.bss) for .bss
    info.o(i.Info_SendMotionState) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendMotionState) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendMotionState) refers to info.o(.bss) for .bss
    info.o(i.Info_SendRPM) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendRPM) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendRPM) refers to info.o(.bss) for .bss
    info.o(i.Info_SendSetTime) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendSetTime) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendSetTime) refers to info.o(.bss) for .bss
    info.o(i.Info_SendSetVoltage) refers to f2d.o(.text) for __aeabi_f2d
    info.o(i.Info_SendSetVoltage) refers to printfa.o(i.__0sprintf) for __2sprintf
    info.o(i.Info_SendSetVoltage) refers to info.o(i.Info_SendCommand) for Info_SendCommand
    info.o(i.Info_SendSetVoltage) refers to info.o(.bss) for .bss
    info.o(i.Info_SendTimeInfo) refers to info.o(i.Info_SendSetTime) for Info_SendSetTime
    info.o(i.Info_SendTimeInfo) refers to info.o(i.Info_SendCountdown) for Info_SendCountdown
    info.o(i.Info_Test) refers to info.o(i.Info_SendSetTime) for Info_SendSetTime
    info.o(i.Info_Test) refers to info.o(i.Info_SendCountdown) for Info_SendCountdown
    info.o(i.Info_Test) refers to info.o(i.Info_SendSetVoltage) for Info_SendSetVoltage
    info.o(i.Info_Test) refers to info.o(i.Info_SendDistance) for Info_SendDistance
    info.o(i.Info_Test) refers to info.o(i.Info_SendRPM) for Info_SendRPM
    info.o(i.Info_Test) refers to info.o(i.Info_SendDirection) for Info_SendDirection
    info.o(i.Info_Test) refers to info.o(i.Info_SendMotionState) for Info_SendMotionState
    info.o(i.TJCPrintf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    info.o(i.TJCPrintf) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    info.o(i.TJCPrintf) refers to usart.o(.bss) for huart1
    dac.o(i.DAC_GetValue) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_GetValue) for HAL_DAC_GetValue
    dac.o(i.DAC_GetValue) refers to dac.o(.bss) for .bss
    dac.o(i.DAC_GetVoltage) refers to dac.o(i.DAC_GetValue) for DAC_GetValue
    dac.o(i.DAC_GetVoltage) refers to ffltui.o(.text) for __aeabi_ui2f
    dac.o(i.DAC_GetVoltage) refers to fmul.o(.text) for __aeabi_fmul
    dac.o(i.DAC_GetVoltage) refers to fdiv.o(.text) for __aeabi_fdiv
    dac.o(i.DAC_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.DAC_Init) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.DAC_Init) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.DAC_Init) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    dac.o(i.DAC_Init) refers to dac.o(i.DAC_SetValue) for DAC_SetValue
    dac.o(i.DAC_Init) refers to dac.o(.bss) for .bss
    dac.o(i.DAC_Init) refers to dac.o(.data) for .data
    dac.o(i.DAC_SetValue) refers to stm32f1xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    dac.o(i.DAC_SetValue) refers to dac.o(.bss) for .bss
    dac.o(i.DAC_SetVoltage) refers to fmul.o(.text) for __aeabi_fmul
    dac.o(i.DAC_SetVoltage) refers to fdiv.o(.text) for __aeabi_fdiv
    dac.o(i.DAC_SetVoltage) refers to ffixui.o(.text) for __aeabi_f2uiz
    dac.o(i.DAC_SetVoltage) refers to dac.o(i.DAC_SetValue) for DAC_SetValue
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to ultrasonic.o(i.Ultrasonic_Trigger) for Ultrasonic_Trigger
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to ultrasonic.o(i.delay_us) for delay_us
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to ffltui.o(.text) for __aeabi_ui2f
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to fmul.o(.text) for __aeabi_fmul
    ultrasonic.o(i.Ultrasonic_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ultrasonic.o(i.Ultrasonic_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ultrasonic.o(i.Ultrasonic_Trigger) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ultrasonic.o(i.Ultrasonic_Trigger) refers to ultrasonic.o(i.delay_us) for delay_us
    ultrasonic.o(i.delay_us) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    fan.o(i.Fan_GetDirection) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_GetSpeed) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_GetVoltage) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_GetVoltageLevel) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fan.o(i.Fan_Init) refers to fan.o(i.Fan_Stop) for Fan_Stop
    fan.o(i.Fan_Init) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_IsRunning) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_IsSpeedAdjusting) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_SetDirection) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fan.o(i.Fan_SetDirection) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_SetSpeed) refers to dac.o(i.DAC_SetValue) for DAC_SetValue
    fan.o(i.Fan_SetSpeed) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fan.o(i.Fan_SetSpeed) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_SetSpeedByLevel) refers to fan.o(i.Fan_SetSpeed) for Fan_SetSpeed
    fan.o(i.Fan_SetSpeedByLevel) refers to fan.o(.constdata) for .constdata
    fan.o(i.Fan_SetSpeedByVoltage) refers to fan.o(i.Fan_SetSpeed) for Fan_SetSpeed
    fan.o(i.Fan_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_Start) refers to fan.o(i.Fan_SetDirection) for Fan_SetDirection
    fan.o(i.Fan_Start) refers to fan.o(i.Fan_SetSpeed) for Fan_SetSpeed
    fan.o(i.Fan_Start) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_StartForward) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_StartForward) refers to fan.o(i.Fan_SetDirection) for Fan_SetDirection
    fan.o(i.Fan_StartForward) refers to fan.o(i.Fan_SetSpeed) for Fan_SetSpeed
    fan.o(i.Fan_StartForward) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_StartReverse) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_StartReverse) refers to fan.o(i.Fan_SetDirection) for Fan_SetDirection
    fan.o(i.Fan_StartReverse) refers to fan.o(i.Fan_SetSpeed) for Fan_SetSpeed
    fan.o(i.Fan_StartReverse) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_StartSpeedAdjust) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_StartSpeedAdjust) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_StartVoltageAdjust) refers to fan.o(i.Fan_GetVoltageLevel) for Fan_GetVoltageLevel
    fan.o(i.Fan_StartVoltageAdjust) refers to fan.o(i.Fan_SetSpeedByLevel) for Fan_SetSpeedByLevel
    fan.o(i.Fan_StartVoltageAdjust) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_StartVoltageAdjust) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Stop) refers to dac.o(i.DAC_SetValue) for DAC_SetValue
    fan.o(i.Fan_Stop) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fan.o(i.Fan_Stop) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_StopSpeedAdjust) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Update) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_Update) refers to fan.o(i.Fan_UpdateSpeedAdjust) for Fan_UpdateSpeedAdjust
    fan.o(i.Fan_Update) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_UpdateSpeedAdjust) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_UpdateSpeedAdjust) refers to ffltui.o(.text) for __aeabi_ui2f
    fan.o(i.Fan_UpdateSpeedAdjust) refers to fdiv.o(.text) for __aeabi_fdiv
    fan.o(i.Fan_UpdateSpeedAdjust) refers to fan.o(i.Fan_SetSpeedByLevel) for Fan_SetSpeedByLevel
    fan.o(i.Fan_UpdateSpeedAdjust) refers to fflti.o(.text) for __aeabi_i2f
    fan.o(i.Fan_UpdateSpeedAdjust) refers to fmul.o(.text) for __aeabi_fmul
    fan.o(i.Fan_UpdateSpeedAdjust) refers to ffixui.o(.text) for __aeabi_f2uiz
    fan.o(i.Fan_UpdateSpeedAdjust) refers to fan.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_ClearFlags) refers to photoswitch.o(.data) for .data
    photoswitch.o(i.PhotoSwitch_ClearFlags) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_GetTriggerTime) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    photoswitch.o(i.PhotoSwitch_Init) refers to photoswitch.o(i.PhotoSwitch_Scan) for PhotoSwitch_Scan
    photoswitch.o(i.PhotoSwitch_Init) refers to photoswitch.o(.data) for .data
    photoswitch.o(i.PhotoSwitch_Init) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_IsChanged) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_IsReleased) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_IsTriggered) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_Scan) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    photoswitch.o(i.PhotoSwitch_Scan) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    photoswitch.o(i.PhotoSwitch_Scan) refers to photoswitch.o(.data) for .data
    photoswitch.o(i.PhotoSwitch_Scan) refers to photoswitch.o(.bss) for .bss
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing system_stm32f1xx.o(.constdata), (16 bytes).
    Removing startup_stm32f103xe.o(HEAP), (1024 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (44 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (68 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (10 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (100 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (68 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (28 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (24 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (156 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (30 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort), (86 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (680 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (1208 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1148 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (58 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (88 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (48 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (48 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (28 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (256 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (296 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (256 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (48 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (118 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (126 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (90 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (40 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler), (218 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (204 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (136 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (106 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (86 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (30 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Receive_IT), (168 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Transmit_IT), (102 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (22 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (22 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (22 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (32 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (32 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (22 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32f1xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32f1xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_DeInit), (30 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_GetValue), (12 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_Start_DMA), (236 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_Stop), (22 bytes).
    Removing stm32f1xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (56 bytes).
    Removing stm32f1xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (24 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (52 bytes).
    Removing stm32f1xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (52 bytes).
    Removing gesture.o(.rev16_text), (4 bytes).
    Removing gesture.o(.revsh_text), (4 bytes).
    Removing gesture.o(.rrx_text), (6 bytes).
    Removing gesture.o(i.Gesture_CheckSlideLogic), (12 bytes).
    Removing gesture.o(i.Gesture_ClearInvalidOperation), (16 bytes).
    Removing gesture.o(i.Gesture_IsValid), (14 bytes).
    Removing info.o(.rev16_text), (4 bytes).
    Removing info.o(.revsh_text), (4 bytes).
    Removing info.o(.rrx_text), (6 bytes).
    Removing info.o(i.Info_SendFanInfo), (58 bytes).
    Removing info.o(i.Info_SendTimeInfo), (18 bytes).
    Removing info.o(i.Info_Test), (60 bytes).
    Removing info.o(i.Info_UpdateAll), (2 bytes).
    Removing info.o(i.TJCPrintf), (64 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.DAC_GetValue), (20 bytes).
    Removing dac.o(i.DAC_GetVoltage), (36 bytes).
    Removing dac.o(i.DAC_SetVoltage), (56 bytes).
    Removing ultrasonic.o(.rev16_text), (4 bytes).
    Removing ultrasonic.o(.revsh_text), (4 bytes).
    Removing ultrasonic.o(.rrx_text), (6 bytes).
    Removing ultrasonic.o(i.Ultrasonic_IsInRange), (18 bytes).
    Removing fan.o(.rev16_text), (4 bytes).
    Removing fan.o(.revsh_text), (4 bytes).
    Removing fan.o(.rrx_text), (6 bytes).
    Removing fan.o(i.Fan_GetSpeed), (12 bytes).
    Removing fan.o(i.Fan_IsSpeedAdjusting), (16 bytes).
    Removing fan.o(i.Fan_Start), (48 bytes).
    Removing fan.o(i.Fan_StartSpeedAdjust), (48 bytes).
    Removing fan.o(i.Fan_StopSpeedAdjust), (12 bytes).
    Removing photoswitch.o(.rev16_text), (4 bytes).
    Removing photoswitch.o(.revsh_text), (4 bytes).
    Removing photoswitch.o(.rrx_text), (6 bytes).
    Removing photoswitch.o(i.PhotoSwitch_IsReleased), (20 bytes).

252 unused section(s) (total 12716 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Drivers/CMSIS/Device/ST/STM32F1xx/Source/Templates/system_stm32f1xx.c 0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../Src/gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ../Src/main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ../Src/stm32f1xx_hal_msp.c               0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Src/stm32f1xx_it.c                    0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Src/usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ..\APP\GESTURE\gesture.c                 0x00000000   Number         0  gesture.o ABSOLUTE
    ..\APP\INFO\info.c                       0x00000000   Number         0  info.o ABSOLUTE
    ..\Drivers\CMSIS\Device\ST\STM32F1xx\Source\Templates\system_stm32f1xx.c 0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dac.c 0x00000000   Number         0  stm32f1xx_hal_dac.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dac_ex.c 0x00000000   Number         0  stm32f1xx_hal_dac_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\HARDWARE\DAC\dac.c                    0x00000000   Number         0  dac.o ABSOLUTE
    ..\HARDWARE\FAN\fan.c                    0x00000000   Number         0  fan.o ABSOLUTE
    ..\HARDWARE\PHOTOSWITCH\photoswitch.c    0x00000000   Number         0  photoswitch.o ABSOLUTE
    ..\HARDWARE\ULTRASONIC\ultrasonic.c      0x00000000   Number         0  ultrasonic.o ABSOLUTE
    ..\Src\gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Src\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\Src\stm32f1xx_hal_msp.c               0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Src\stm32f1xx_it.c                    0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Src\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\\APP\\GESTURE\\gesture.c              0x00000000   Number         0  gesture.o ABSOLUTE
    ..\\APP\\INFO\\info.c                    0x00000000   Number         0  info.o ABSOLUTE
    ..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dac.c 0x00000000   Number         0  stm32f1xx_hal_dac.o ABSOLUTE
    ..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dac_ex.c 0x00000000   Number         0  stm32f1xx_hal_dac_ex.o ABSOLUTE
    ..\\HARDWARE\\DAC\\dac.c                 0x00000000   Number         0  dac.o ABSOLUTE
    ..\\HARDWARE\\FAN\\fan.c                 0x00000000   Number         0  fan.o ABSOLUTE
    ..\\HARDWARE\\PHOTOSWITCH\\photoswitch.c 0x00000000   Number         0  photoswitch.o ABSOLUTE
    ..\\HARDWARE\\ULTRASONIC\\ultrasonic.c   0x00000000   Number         0  ultrasonic.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xe.s                    0x00000000   Number         0  startup_stm32f103xe.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f103xe.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f103xe.o(.text)
    .text                                    0x08000168   Section        0  llushr.o(.text)
    .text                                    0x08000188   Section        0  memseta.o(.text)
    .text                                    0x080001ac   Section        0  strlen.o(.text)
    .text                                    0x080001ba   Section        0  fadd.o(.text)
    .text                                    0x0800026a   Section        0  fmul.o(.text)
    .text                                    0x080002ce   Section        0  fdiv.o(.text)
    .text                                    0x0800034a   Section        0  fflti.o(.text)
    .text                                    0x0800035c   Section        0  ffltui.o(.text)
    .text                                    0x08000366   Section        0  ffixui.o(.text)
    .text                                    0x0800038e   Section        0  f2d.o(.text)
    .text                                    0x080003b4   Section       20  cfcmple.o(.text)
    .text                                    0x080003c8   Section       20  cfrcmple.o(.text)
    .text                                    0x080003dc   Section        0  uidiv.o(.text)
    .text                                    0x08000408   Section        0  uldiv.o(.text)
    .text                                    0x0800046a   Section        0  fepilogue.o(.text)
    .text                                    0x0800046a   Section        0  iusefp.o(.text)
    .text                                    0x080004d8   Section        0  dadd.o(.text)
    .text                                    0x08000626   Section        0  dmul.o(.text)
    .text                                    0x0800070a   Section        0  ddiv.o(.text)
    .text                                    0x080007e8   Section        0  dfixul.o(.text)
    .text                                    0x08000818   Section       48  cdrcmple.o(.text)
    .text                                    0x08000848   Section       36  init.o(.text)
    .text                                    0x0800086c   Section        0  llshl.o(.text)
    .text                                    0x0800088a   Section        0  llsshr.o(.text)
    .text                                    0x080008ae   Section        0  depilogue.o(.text)
    i.Check_Distance_Stability               0x08000968   Section        0  main.o(i.Check_Distance_Stability)
    i.DAC_Init                               0x080009c0   Section        0  dac.o(i.DAC_Init)
    i.DAC_SetValue                           0x08000a48   Section        0  dac.o(i.DAC_SetValue)
    i.Fan_GetDirection                       0x08000a64   Section        0  fan.o(i.Fan_GetDirection)
    i.Fan_GetVoltage                         0x08000a70   Section        0  fan.o(i.Fan_GetVoltage)
    i.Fan_GetVoltageLevel                    0x08000a7c   Section        0  fan.o(i.Fan_GetVoltageLevel)
    i.Fan_Init                               0x08000a98   Section        0  fan.o(i.Fan_Init)
    i.Fan_IsRunning                          0x08000b10   Section        0  fan.o(i.Fan_IsRunning)
    i.Fan_SetDirection                       0x08000b1c   Section        0  fan.o(i.Fan_SetDirection)
    i.Fan_SetSpeed                           0x08000b44   Section        0  fan.o(i.Fan_SetSpeed)
    i.Fan_SetSpeedByLevel                    0x08000b88   Section        0  fan.o(i.Fan_SetSpeedByLevel)
    i.Fan_SetSpeedByVoltage                  0x08000b9c   Section        0  fan.o(i.Fan_SetSpeedByVoltage)
    i.Fan_StartForward                       0x08000bb4   Section        0  fan.o(i.Fan_StartForward)
    i.Fan_StartReverse                       0x08000be0   Section        0  fan.o(i.Fan_StartReverse)
    i.Fan_StartVoltageAdjust                 0x08000c0c   Section        0  fan.o(i.Fan_StartVoltageAdjust)
    i.Fan_Stop                               0x08000c54   Section        0  fan.o(i.Fan_Stop)
    i.Fan_Update                             0x08000c80   Section        0  fan.o(i.Fan_Update)
    i.Fan_UpdateSpeedAdjust                  0x08000ca0   Section        0  fan.o(i.Fan_UpdateSpeedAdjust)
    i.Gesture_GetName                        0x08000d0c   Section        0  gesture.o(i.Gesture_GetName)
    i.Gesture_Init                           0x08000d28   Section        0  gesture.o(i.Gesture_Init)
    i.Gesture_Recognize                      0x08000d48   Section        0  gesture.o(i.Gesture_Recognize)
    i.Gesture_Reset                          0x08000e40   Section        0  gesture.o(i.Gesture_Reset)
    i.Get_Time_From_Distance                 0x08000e58   Section        0  main.o(i.Get_Time_From_Distance)
    i.Get_Voltage_From_Distance              0x08000e7c   Section        0  main.o(i.Get_Voltage_From_Distance)
    i.HAL_DAC_ConfigChannel                  0x08000ea0   Section        0  stm32f1xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_Init                           0x08000ee2   Section        0  stm32f1xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x08000f0a   Section        0  stm32f1xx_hal_dac.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_SetValue                       0x08000f0c   Section        0  stm32f1xx_hal_dac.o(i.HAL_DAC_SetValue)
    i.HAL_DAC_Start                          0x08000f26   Section        0  stm32f1xx_hal_dac.o(i.HAL_DAC_Start)
    i.HAL_Delay                              0x08000f7a   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000f90   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08001178   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08001182   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800118c   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001198   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080011a8   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080011cc   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080011f0   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_SetPriority                   0x0800124c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800128c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080012b0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001538   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x0800156c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001598   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080015c4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800164c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_CLKSourceConfig            0x08001c7e   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Callback                   0x08001c96   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback)
    i.HAL_SYSTICK_Config                     0x08001c98   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_SYSTICK_IRQHandler                 0x08001cc0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler)
    i.HAL_UART_Init                          0x08001cc8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001d28   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x08001d80   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.Info_GetRpmByVoltage                   0x08001e4c   Section        0  info.o(i.Info_GetRpmByVoltage)
    i.Info_Init                              0x08001ee0   Section        0  info.o(i.Info_Init)
    i.Info_SendCommand                       0x08001f1c   Section        0  info.o(i.Info_SendCommand)
    i.Info_SendCountdown                     0x08001f54   Section        0  info.o(i.Info_SendCountdown)
    i.Info_SendDirection                     0x08001f88   Section        0  info.o(i.Info_SendDirection)
    i.Info_SendDistance                      0x08001ff0   Section        0  info.o(i.Info_SendDistance)
    i.Info_SendFanInfoWithLookup             0x08002028   Section        0  info.o(i.Info_SendFanInfoWithLookup)
    i.Info_SendFinalDistance                 0x08002068   Section        0  info.o(i.Info_SendFinalDistance)
    i.Info_SendMotionState                   0x080020ac   Section        0  info.o(i.Info_SendMotionState)
    i.Info_SendRPM                           0x080020dc   Section        0  info.o(i.Info_SendRPM)
    i.Info_SendSetTime                       0x0800210c   Section        0  info.o(i.Info_SendSetTime)
    i.Info_SendSetVoltage                    0x08002140   Section        0  info.o(i.Info_SendSetVoltage)
    i.MX_GPIO_Init                           0x08002178   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x08002194   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.NVIC_SetPriority                       0x080021bc   Section        0  stm32f1xx_hal_cortex.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x080021bd   Thumb Code    32  stm32f1xx_hal_cortex.o(i.NVIC_SetPriority)
    i.PhotoSwitch_ClearFlags                 0x080021dc   Section        0  photoswitch.o(i.PhotoSwitch_ClearFlags)
    i.PhotoSwitch_GetTriggerTime             0x08002200   Section        0  photoswitch.o(i.PhotoSwitch_GetTriggerTime)
    i.PhotoSwitch_Init                       0x08002218   Section        0  photoswitch.o(i.PhotoSwitch_Init)
    i.PhotoSwitch_IsChanged                  0x080022a0   Section        0  photoswitch.o(i.PhotoSwitch_IsChanged)
    i.PhotoSwitch_IsTriggered                0x080022b4   Section        0  photoswitch.o(i.PhotoSwitch_IsTriggered)
    i.PhotoSwitch_Scan                       0x080022cc   Section        0  photoswitch.o(i.PhotoSwitch_Scan)
    i.Process_Gesture                        0x08002350   Section        0  main.o(i.Process_Gesture)
    i.SysTick_Handler                        0x08002604   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002612   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002664   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.System_Init                            0x080026ac   Section        0  main.o(i.System_Init)
    i.UART_SetConfig                         0x080026e4   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080026e5   Thumb Code   220  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x080027c4   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080027c5   Thumb Code   146  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.Ultrasonic_GetDistance                 0x08002858   Section        0  ultrasonic.o(i.Ultrasonic_GetDistance)
    i.Ultrasonic_Init                        0x080028cc   Section        0  ultrasonic.o(i.Ultrasonic_Init)
    i.Ultrasonic_Trigger                     0x08002924   Section        0  ultrasonic.o(i.Ultrasonic_Trigger)
    i.__0printf                              0x0800294c   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x0800296c   Section        0  printfa.o(i.__0sprintf)
    i.__scatterload_copy                     0x08002994   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080029a2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080029a4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080029b4   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080029b5   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08002b38   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08002b39   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08003214   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08003215   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003238   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003239   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08003266   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08003267   Thumb Code    10  printfa.o(i._sputc)
    i.delay_us                               0x08003270   Section        0  ultrasonic.o(i.delay_us)
    delay_us                                 0x08003271   Thumb Code    18  ultrasonic.o(i.delay_us)
    i.fputc                                  0x0800328c   Section        0  usart.o(i.fputc)
    i.main                                   0x080032a4   Section        0  main.o(i.main)
    .constdata                               0x0800333c   Section      208  main.o(.constdata)
    .constdata                               0x0800340c   Section       16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x0800341c   Section      152  info.o(.constdata)
    .constdata                               0x080034b4   Section       50  fan.o(.constdata)
    .conststring                             0x080034e8   Section      135  main.o(.conststring)
    .conststring                             0x08003570   Section       77  gesture.o(.conststring)
    .data                                    0x20000000   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000004   Section       32  main.o(.data)
    .data                                    0x20000024   Section        4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000024   Data           4  stm32f1xx_hal.o(.data)
    .data                                    0x20000028   Section       40  gesture.o(.data)
    gesture_names                            0x20000028   Data          40  gesture.o(.data)
    .data                                    0x20000050   Section        8  dac.o(.data)
    .data                                    0x20000058   Section        4  photoswitch.o(.data)
    .data                                    0x2000005c   Section        4  stdout.o(.data)
    .bss                                     0x20000060   Section       64  usart.o(.bss)
    .bss                                     0x200000a0   Section       28  gesture.o(.bss)
    .bss                                     0x200000bc   Section      100  info.o(.bss)
    .bss                                     0x20000120   Section       20  dac.o(.bss)
    .bss                                     0x20000134   Section       32  fan.o(.bss)
    .bss                                     0x20000154   Section       28  photoswitch.o(.bss)
    STACK                                    0x20000170   Section      512  startup_stm32f103xe.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f103xe.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xe.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f103xe.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f103xe.o(.text)
    NMI_Handler                              0x0800014d   Thumb Code     2  startup_stm32f103xe.o(.text)
    HardFault_Handler                        0x0800014f   Thumb Code     2  startup_stm32f103xe.o(.text)
    MemManage_Handler                        0x08000151   Thumb Code     2  startup_stm32f103xe.o(.text)
    BusFault_Handler                         0x08000153   Thumb Code     2  startup_stm32f103xe.o(.text)
    UsageFault_Handler                       0x08000155   Thumb Code     2  startup_stm32f103xe.o(.text)
    SVC_Handler                              0x08000157   Thumb Code     2  startup_stm32f103xe.o(.text)
    DebugMon_Handler                         0x08000159   Thumb Code     2  startup_stm32f103xe.o(.text)
    PendSV_Handler                           0x0800015b   Thumb Code     2  startup_stm32f103xe.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_Alarm_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART1_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    __aeabi_llsr                             0x08000169   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000169   Thumb Code     0  llushr.o(.text)
    __aeabi_memset                           0x08000189   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000189   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000189   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000197   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000197   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000197   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800019b   Thumb Code    18  memseta.o(.text)
    strlen                                   0x080001ad   Thumb Code    14  strlen.o(.text)
    __aeabi_fadd                             0x080001bb   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x0800025f   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08000265   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x0800026b   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x080002cf   Thumb Code   124  fdiv.o(.text)
    __aeabi_i2f                              0x0800034b   Thumb Code    18  fflti.o(.text)
    __aeabi_ui2f                             0x0800035d   Thumb Code    10  ffltui.o(.text)
    __aeabi_f2uiz                            0x08000367   Thumb Code    40  ffixui.o(.text)
    __aeabi_f2d                              0x0800038f   Thumb Code    38  f2d.o(.text)
    __aeabi_cfcmpeq                          0x080003b5   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x080003b5   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x080003c9   Thumb Code    20  cfrcmple.o(.text)
    __aeabi_uidiv                            0x080003dd   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080003dd   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000409   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x0800046b   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800046b   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800047d   Thumb Code    92  fepilogue.o(.text)
    __aeabi_dadd                             0x080004d9   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800061b   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000621   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000627   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x0800070b   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x080007e9   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000819   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000849   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000849   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0800086d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800086d   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x0800088b   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800088b   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080008af   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080008cd   Thumb Code   156  depilogue.o(.text)
    Check_Distance_Stability                 0x08000969   Thumb Code    82  main.o(i.Check_Distance_Stability)
    DAC_Init                                 0x080009c1   Thumb Code   114  dac.o(i.DAC_Init)
    DAC_SetValue                             0x08000a49   Thumb Code    22  dac.o(i.DAC_SetValue)
    Fan_GetDirection                         0x08000a65   Thumb Code     6  fan.o(i.Fan_GetDirection)
    Fan_GetVoltage                           0x08000a71   Thumb Code     6  fan.o(i.Fan_GetVoltage)
    Fan_GetVoltageLevel                      0x08000a7d   Thumb Code    22  fan.o(i.Fan_GetVoltageLevel)
    Fan_Init                                 0x08000a99   Thumb Code   106  fan.o(i.Fan_Init)
    Fan_IsRunning                            0x08000b11   Thumb Code     6  fan.o(i.Fan_IsRunning)
    Fan_SetDirection                         0x08000b1d   Thumb Code    30  fan.o(i.Fan_SetDirection)
    Fan_SetSpeed                             0x08000b45   Thumb Code    58  fan.o(i.Fan_SetSpeed)
    Fan_SetSpeedByLevel                      0x08000b89   Thumb Code    16  fan.o(i.Fan_SetSpeedByLevel)
    Fan_SetSpeedByVoltage                    0x08000b9d   Thumb Code    24  fan.o(i.Fan_SetSpeedByVoltage)
    Fan_StartForward                         0x08000bb5   Thumb Code    38  fan.o(i.Fan_StartForward)
    Fan_StartReverse                         0x08000be1   Thumb Code    40  fan.o(i.Fan_StartReverse)
    Fan_StartVoltageAdjust                   0x08000c0d   Thumb Code    66  fan.o(i.Fan_StartVoltageAdjust)
    Fan_Stop                                 0x08000c55   Thumb Code    36  fan.o(i.Fan_Stop)
    Fan_Update                               0x08000c81   Thumb Code    26  fan.o(i.Fan_Update)
    Fan_UpdateSpeedAdjust                    0x08000ca1   Thumb Code   102  fan.o(i.Fan_UpdateSpeedAdjust)
    Gesture_GetName                          0x08000d0d   Thumb Code    16  gesture.o(i.Gesture_GetName)
    Gesture_Init                             0x08000d29   Thumb Code    28  gesture.o(i.Gesture_Init)
    Gesture_Recognize                        0x08000d49   Thumb Code   244  gesture.o(i.Gesture_Recognize)
    Gesture_Reset                            0x08000e41   Thumb Code    20  gesture.o(i.Gesture_Reset)
    Get_Time_From_Distance                   0x08000e59   Thumb Code    32  main.o(i.Get_Time_From_Distance)
    Get_Voltage_From_Distance                0x08000e7d   Thumb Code    32  main.o(i.Get_Voltage_From_Distance)
    HAL_DAC_ConfigChannel                    0x08000ea1   Thumb Code    66  stm32f1xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_Init                             0x08000ee3   Thumb Code    40  stm32f1xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08000f0b   Thumb Code     2  stm32f1xx_hal_dac.o(i.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x08000f0d   Thumb Code    26  stm32f1xx_hal_dac.o(i.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x08000f27   Thumb Code    84  stm32f1xx_hal_dac.o(i.HAL_DAC_Start)
    HAL_Delay                                0x08000f7b   Thumb Code    22  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000f91   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08001179   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08001183   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800118d   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001199   Thumb Code    10  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080011a9   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080011cd   Thumb Code    34  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080011f1   Thumb Code    86  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x0800124d   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800128d   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080012b1   Thumb Code   638  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001539   Thumb Code    38  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x0800156d   Thumb Code    36  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001599   Thumb Code    36  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080015c5   Thumb Code   102  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800164d   Thumb Code  1586  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_CLKSourceConfig              0x08001c7f   Thumb Code    24  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Callback                     0x08001c97   Thumb Code     2  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback)
    HAL_SYSTICK_Config                       0x08001c99   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_SYSTICK_IRQHandler                   0x08001cc1   Thumb Code     8  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler)
    HAL_UART_Init                            0x08001cc9   Thumb Code    94  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001d29   Thumb Code    76  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08001d81   Thumb Code   204  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    Info_GetRpmByVoltage                     0x08001e4d   Thumb Code   134  info.o(i.Info_GetRpmByVoltage)
    Info_Init                                0x08001ee1   Thumb Code    56  info.o(i.Info_Init)
    Info_SendCommand                         0x08001f1d   Thumb Code    46  info.o(i.Info_SendCommand)
    Info_SendCountdown                       0x08001f55   Thumb Code    22  info.o(i.Info_SendCountdown)
    Info_SendDirection                       0x08001f89   Thumb Code    38  info.o(i.Info_SendDirection)
    Info_SendDistance                        0x08001ff1   Thumb Code    28  info.o(i.Info_SendDistance)
    Info_SendFanInfoWithLookup               0x08002029   Thumb Code    62  info.o(i.Info_SendFanInfoWithLookup)
    Info_SendFinalDistance                   0x08002069   Thumb Code    22  info.o(i.Info_SendFinalDistance)
    Info_SendMotionState                     0x080020ad   Thumb Code    28  info.o(i.Info_SendMotionState)
    Info_SendRPM                             0x080020dd   Thumb Code    22  info.o(i.Info_SendRPM)
    Info_SendSetTime                         0x0800210d   Thumb Code    22  info.o(i.Info_SendSetTime)
    Info_SendSetVoltage                      0x08002141   Thumb Code    28  info.o(i.Info_SendSetVoltage)
    MX_GPIO_Init                             0x08002179   Thumb Code    22  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x08002195   Thumb Code    32  usart.o(i.MX_USART1_UART_Init)
    PhotoSwitch_ClearFlags                   0x080021dd   Thumb Code    26  photoswitch.o(i.PhotoSwitch_ClearFlags)
    PhotoSwitch_GetTriggerTime               0x08002201   Thumb Code    18  photoswitch.o(i.PhotoSwitch_GetTriggerTime)
    PhotoSwitch_Init                         0x08002219   Thumb Code   120  photoswitch.o(i.PhotoSwitch_Init)
    PhotoSwitch_IsChanged                    0x080022a1   Thumb Code    16  photoswitch.o(i.PhotoSwitch_IsChanged)
    PhotoSwitch_IsTriggered                  0x080022b5   Thumb Code    18  photoswitch.o(i.PhotoSwitch_IsTriggered)
    PhotoSwitch_Scan                         0x080022cd   Thumb Code   120  photoswitch.o(i.PhotoSwitch_Scan)
    Process_Gesture                          0x08002351   Thumb Code   290  main.o(i.Process_Gesture)
    SysTick_Handler                          0x08002605   Thumb Code    14  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002613   Thumb Code    82  main.o(i.SystemClock_Config)
    SystemInit                               0x08002665   Thumb Code    56  system_stm32f1xx.o(i.SystemInit)
    System_Init                              0x080026ad   Thumb Code    50  main.o(i.System_Init)
    Ultrasonic_GetDistance                   0x08002859   Thumb Code   104  ultrasonic.o(i.Ultrasonic_GetDistance)
    Ultrasonic_Init                          0x080028cd   Thumb Code    80  ultrasonic.o(i.Ultrasonic_Init)
    Ultrasonic_Trigger                       0x08002925   Thumb Code    34  ultrasonic.o(i.Ultrasonic_Trigger)
    __0printf                                0x0800294d   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x0800294d   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x0800294d   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x0800294d   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x0800294d   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x0800296d   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x0800296d   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x0800296d   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x0800296d   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x0800296d   Thumb Code     0  printfa.o(i.__0sprintf)
    __scatterload_copy                       0x08002995   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080029a3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080029a5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x0800328d   Thumb Code    20  usart.o(i.fputc)
    main                                     0x080032a5   Thumb Code   144  main.o(i.main)
    distance_time_table                      0x0800333c   Data         104  main.o(.constdata)
    distance_voltage_table                   0x080033a4   Data         104  main.o(.constdata)
    aAPBAHBPrescTable                        0x0800340c   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    fan_voltage_rpm_table                    0x0800341c   Data         152  info.o(.constdata)
    fan_voltage_dac_table                    0x080034b4   Data          50  fan.o(.constdata)
    Region$$Table$$Base                      0x080035c0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080035e0   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f1xx.o(.data)
    current_gesture                          0x20000004   Data           1  main.o(.data)
    info_direction                           0x20000005   Data           1  main.o(.data)
    is_distance_stable                       0x20000006   Data           1  main.o(.data)
    current_rounded_distance                 0x20000008   Data           2  main.o(.data)
    last_stable_distance                     0x2000000a   Data           2  main.o(.data)
    final_d                                  0x2000000c   Data           2  main.o(.data)
    final_d_ready                            0x2000000e   Data           2  main.o(.data)
    distance                                 0x20000010   Data           4  main.o(.data)
    dac_voltage                              0x20000014   Data           4  main.o(.data)
    last_display_update                      0x20000018   Data           4  main.o(.data)
    actual_voltage                           0x2000001c   Data           4  main.o(.data)
    stability_start_time                     0x20000020   Data           4  main.o(.data)
    sConfig                                  0x20000050   Data           8  dac.o(.data)
    i                                        0x20000058   Data           4  photoswitch.o(.data)
    __stdout                                 0x2000005c   Data           4  stdout.o(.data)
    huart1                                   0x20000060   Data          64  usart.o(.bss)
    gesture_status                           0x200000a0   Data          16  gesture.o(.bss)
    slide_detection                          0x200000b0   Data          12  gesture.o(.bss)
    info_buffer                              0x200000bc   Data         100  info.o(.bss)
    hdac                                     0x20000120   Data          20  dac.o(.bss)
    fan_control                              0x20000134   Data          32  fan.o(.bss)
    switch_status                            0x20000154   Data          28  photoswitch.o(.bss)
    __initial_sp                             0x20000370   Data           0  startup_stm32f103xe.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003640, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000035e0, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          120    RESET               startup_stm32f103xe.o
    0x08000130   0x08000130   0x00000000   Code   RO         2280  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         2568    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         2571    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         2573    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         2575    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         2576    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         2578    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         2580    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         2569    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          121    .text               startup_stm32f103xe.o
    0x08000168   0x08000168   0x00000020   Code   RO         2283    .text               mc_w.l(llushr.o)
    0x08000188   0x08000188   0x00000024   Code   RO         2285    .text               mc_w.l(memseta.o)
    0x080001ac   0x080001ac   0x0000000e   Code   RO         2287    .text               mc_w.l(strlen.o)
    0x080001ba   0x080001ba   0x000000b0   Code   RO         2550    .text               mf_w.l(fadd.o)
    0x0800026a   0x0800026a   0x00000064   Code   RO         2552    .text               mf_w.l(fmul.o)
    0x080002ce   0x080002ce   0x0000007c   Code   RO         2554    .text               mf_w.l(fdiv.o)
    0x0800034a   0x0800034a   0x00000012   Code   RO         2556    .text               mf_w.l(fflti.o)
    0x0800035c   0x0800035c   0x0000000a   Code   RO         2558    .text               mf_w.l(ffltui.o)
    0x08000366   0x08000366   0x00000028   Code   RO         2560    .text               mf_w.l(ffixui.o)
    0x0800038e   0x0800038e   0x00000026   Code   RO         2562    .text               mf_w.l(f2d.o)
    0x080003b4   0x080003b4   0x00000014   Code   RO         2564    .text               mf_w.l(cfcmple.o)
    0x080003c8   0x080003c8   0x00000014   Code   RO         2566    .text               mf_w.l(cfrcmple.o)
    0x080003dc   0x080003dc   0x0000002c   Code   RO         2583    .text               mc_w.l(uidiv.o)
    0x08000408   0x08000408   0x00000062   Code   RO         2585    .text               mc_w.l(uldiv.o)
    0x0800046a   0x0800046a   0x00000000   Code   RO         2587    .text               mc_w.l(iusefp.o)
    0x0800046a   0x0800046a   0x0000006e   Code   RO         2588    .text               mf_w.l(fepilogue.o)
    0x080004d8   0x080004d8   0x0000014e   Code   RO         2590    .text               mf_w.l(dadd.o)
    0x08000626   0x08000626   0x000000e4   Code   RO         2592    .text               mf_w.l(dmul.o)
    0x0800070a   0x0800070a   0x000000de   Code   RO         2594    .text               mf_w.l(ddiv.o)
    0x080007e8   0x080007e8   0x00000030   Code   RO         2596    .text               mf_w.l(dfixul.o)
    0x08000818   0x08000818   0x00000030   Code   RO         2598    .text               mf_w.l(cdrcmple.o)
    0x08000848   0x08000848   0x00000024   Code   RO         2600    .text               mc_w.l(init.o)
    0x0800086c   0x0800086c   0x0000001e   Code   RO         2602    .text               mc_w.l(llshl.o)
    0x0800088a   0x0800088a   0x00000024   Code   RO         2604    .text               mc_w.l(llsshr.o)
    0x080008ae   0x080008ae   0x000000ba   Code   RO         2606    .text               mf_w.l(depilogue.o)
    0x08000968   0x08000968   0x00000058   Code   RO          234    i.Check_Distance_Stability  main.o
    0x080009c0   0x080009c0   0x00000088   Code   RO         1967    i.DAC_Init          dac.o
    0x08000a48   0x08000a48   0x0000001c   Code   RO         1968    i.DAC_SetValue      dac.o
    0x08000a64   0x08000a64   0x0000000c   Code   RO         2069    i.Fan_GetDirection  fan.o
    0x08000a70   0x08000a70   0x0000000c   Code   RO         2071    i.Fan_GetVoltage    fan.o
    0x08000a7c   0x08000a7c   0x0000001c   Code   RO         2072    i.Fan_GetVoltageLevel  fan.o
    0x08000a98   0x08000a98   0x00000078   Code   RO         2073    i.Fan_Init          fan.o
    0x08000b10   0x08000b10   0x0000000c   Code   RO         2074    i.Fan_IsRunning     fan.o
    0x08000b1c   0x08000b1c   0x00000028   Code   RO         2076    i.Fan_SetDirection  fan.o
    0x08000b44   0x08000b44   0x00000044   Code   RO         2077    i.Fan_SetSpeed      fan.o
    0x08000b88   0x08000b88   0x00000014   Code   RO         2078    i.Fan_SetSpeedByLevel  fan.o
    0x08000b9c   0x08000b9c   0x00000018   Code   RO         2079    i.Fan_SetSpeedByVoltage  fan.o
    0x08000bb4   0x08000bb4   0x0000002c   Code   RO         2081    i.Fan_StartForward  fan.o
    0x08000be0   0x08000be0   0x0000002c   Code   RO         2082    i.Fan_StartReverse  fan.o
    0x08000c0c   0x08000c0c   0x00000048   Code   RO         2084    i.Fan_StartVoltageAdjust  fan.o
    0x08000c54   0x08000c54   0x0000002c   Code   RO         2085    i.Fan_Stop          fan.o
    0x08000c80   0x08000c80   0x00000020   Code   RO         2087    i.Fan_Update        fan.o
    0x08000ca0   0x08000ca0   0x0000006c   Code   RO         2088    i.Fan_UpdateSpeedAdjust  fan.o
    0x08000d0c   0x08000d0c   0x0000001c   Code   RO         1759    i.Gesture_GetName   gesture.o
    0x08000d28   0x08000d28   0x00000020   Code   RO         1760    i.Gesture_Init      gesture.o
    0x08000d48   0x08000d48   0x000000f8   Code   RO         1762    i.Gesture_Recognize  gesture.o
    0x08000e40   0x08000e40   0x00000018   Code   RO         1763    i.Gesture_Reset     gesture.o
    0x08000e58   0x08000e58   0x00000024   Code   RO          235    i.Get_Time_From_Distance  main.o
    0x08000e7c   0x08000e7c   0x00000024   Code   RO          236    i.Get_Voltage_From_Distance  main.o
    0x08000ea0   0x08000ea0   0x00000042   Code   RO         1550    i.HAL_DAC_ConfigChannel  stm32f1xx_hal_dac.o
    0x08000ee2   0x08000ee2   0x00000028   Code   RO         1558    i.HAL_DAC_Init      stm32f1xx_hal_dac.o
    0x08000f0a   0x08000f0a   0x00000002   Code   RO         1560    i.HAL_DAC_MspInit   stm32f1xx_hal_dac.o
    0x08000f0c   0x08000f0c   0x0000001a   Code   RO         1561    i.HAL_DAC_SetValue  stm32f1xx_hal_dac.o
    0x08000f26   0x08000f26   0x00000054   Code   RO         1562    i.HAL_DAC_Start     stm32f1xx_hal_dac.o
    0x08000f7a   0x08000f7a   0x00000016   Code   RO          803    i.HAL_Delay         stm32f1xx_hal.o
    0x08000f90   0x08000f90   0x000001e8   Code   RO         1005    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08001178   0x08001178   0x0000000a   Code   RO         1007    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x08001182   0x08001182   0x0000000a   Code   RO         1009    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x0800118c   0x0800118c   0x0000000c   Code   RO          807    i.HAL_GetTick       stm32f1xx_hal.o
    0x08001198   0x08001198   0x00000010   Code   RO          808    i.HAL_IncTick       stm32f1xx_hal.o
    0x080011a8   0x080011a8   0x00000024   Code   RO          809    i.HAL_Init          stm32f1xx_hal.o
    0x080011cc   0x080011cc   0x00000022   Code   RO          810    i.HAL_InitTick      stm32f1xx_hal.o
    0x080011ee   0x080011ee   0x00000002   PAD
    0x080011f0   0x080011f0   0x0000005c   Code   RO          402    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x0800124c   0x0800124c   0x00000040   Code   RO         1438    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x0800128c   0x0800128c   0x00000024   Code   RO         1439    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x080012b0   0x080012b0   0x00000288   Code   RO          530    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08001538   0x08001538   0x00000034   Code   RO          535    i.HAL_RCC_GetHCLKFreq  stm32f1xx_hal_rcc.o
    0x0800156c   0x0800156c   0x0000002c   Code   RO          537    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08001598   0x08001598   0x0000002c   Code   RO          538    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080015c4   0x080015c4   0x00000088   Code   RO          539    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x0800164c   0x0800164c   0x00000632   Code   RO          542    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001c7e   0x08001c7e   0x00000018   Code   RO         1441    i.HAL_SYSTICK_CLKSourceConfig  stm32f1xx_hal_cortex.o
    0x08001c96   0x08001c96   0x00000002   Code   RO         1442    i.HAL_SYSTICK_Callback  stm32f1xx_hal_cortex.o
    0x08001c98   0x08001c98   0x00000028   Code   RO         1443    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08001cc0   0x08001cc0   0x00000008   Code   RO         1444    i.HAL_SYSTICK_IRQHandler  stm32f1xx_hal_cortex.o
    0x08001cc8   0x08001cc8   0x0000005e   Code   RO         1084    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x08001d26   0x08001d26   0x00000002   PAD
    0x08001d28   0x08001d28   0x00000058   Code   RO          187    i.HAL_UART_MspInit  usart.o
    0x08001d80   0x08001d80   0x000000cc   Code   RO         1092    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x08001e4c   0x08001e4c   0x00000094   Code   RO         1829    i.Info_GetRpmByVoltage  info.o
    0x08001ee0   0x08001ee0   0x0000003c   Code   RO         1830    i.Info_Init         info.o
    0x08001f1c   0x08001f1c   0x00000038   Code   RO         1831    i.Info_SendCommand  info.o
    0x08001f54   0x08001f54   0x00000034   Code   RO         1832    i.Info_SendCountdown  info.o
    0x08001f88   0x08001f88   0x00000068   Code   RO         1833    i.Info_SendDirection  info.o
    0x08001ff0   0x08001ff0   0x00000038   Code   RO         1834    i.Info_SendDistance  info.o
    0x08002028   0x08002028   0x0000003e   Code   RO         1836    i.Info_SendFanInfoWithLookup  info.o
    0x08002066   0x08002066   0x00000002   PAD
    0x08002068   0x08002068   0x00000044   Code   RO         1837    i.Info_SendFinalDistance  info.o
    0x080020ac   0x080020ac   0x00000030   Code   RO         1838    i.Info_SendMotionState  info.o
    0x080020dc   0x080020dc   0x00000030   Code   RO         1839    i.Info_SendRPM      info.o
    0x0800210c   0x0800210c   0x00000034   Code   RO         1840    i.Info_SendSetTime  info.o
    0x08002140   0x08002140   0x00000038   Code   RO         1841    i.Info_SendSetVoltage  info.o
    0x08002178   0x08002178   0x0000001c   Code   RO          128    i.MX_GPIO_Init      gpio.o
    0x08002194   0x08002194   0x00000028   Code   RO          188    i.MX_USART1_UART_Init  usart.o
    0x080021bc   0x080021bc   0x00000020   Code   RO         1445    i.NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080021dc   0x080021dc   0x00000024   Code   RO         2216    i.PhotoSwitch_ClearFlags  photoswitch.o
    0x08002200   0x08002200   0x00000018   Code   RO         2217    i.PhotoSwitch_GetTriggerTime  photoswitch.o
    0x08002218   0x08002218   0x00000088   Code   RO         2218    i.PhotoSwitch_Init  photoswitch.o
    0x080022a0   0x080022a0   0x00000014   Code   RO         2219    i.PhotoSwitch_IsChanged  photoswitch.o
    0x080022b4   0x080022b4   0x00000018   Code   RO         2221    i.PhotoSwitch_IsTriggered  photoswitch.o
    0x080022cc   0x080022cc   0x00000084   Code   RO         2222    i.PhotoSwitch_Scan  photoswitch.o
    0x08002350   0x08002350   0x000002b4   Code   RO          237    i.Process_Gesture   main.o
    0x08002604   0x08002604   0x0000000e   Code   RO          159    i.SysTick_Handler   stm32f1xx_it.o
    0x08002612   0x08002612   0x00000052   Code   RO          238    i.SystemClock_Config  main.o
    0x08002664   0x08002664   0x00000048   Code   RO            5    i.SystemInit        system_stm32f1xx.o
    0x080026ac   0x080026ac   0x00000038   Code   RO          239    i.System_Init       main.o
    0x080026e4   0x080026e4   0x000000e0   Code   RO         1103    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x080027c4   0x080027c4   0x00000092   Code   RO         1105    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08002856   0x08002856   0x00000002   PAD
    0x08002858   0x08002858   0x00000074   Code   RO         2019    i.Ultrasonic_GetDistance  ultrasonic.o
    0x080028cc   0x080028cc   0x00000058   Code   RO         2020    i.Ultrasonic_Init   ultrasonic.o
    0x08002924   0x08002924   0x00000028   Code   RO         2022    i.Ultrasonic_Trigger  ultrasonic.o
    0x0800294c   0x0800294c   0x00000020   Code   RO         2522    i.__0printf         mc_w.l(printfa.o)
    0x0800296c   0x0800296c   0x00000028   Code   RO         2524    i.__0sprintf        mc_w.l(printfa.o)
    0x08002994   0x08002994   0x0000000e   Code   RO         2610    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080029a2   0x080029a2   0x00000002   Code   RO         2611    i.__scatterload_null  mc_w.l(handlers.o)
    0x080029a4   0x080029a4   0x0000000e   Code   RO         2612    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080029b2   0x080029b2   0x00000002   PAD
    0x080029b4   0x080029b4   0x00000184   Code   RO         2529    i._fp_digits        mc_w.l(printfa.o)
    0x08002b38   0x08002b38   0x000006dc   Code   RO         2530    i._printf_core      mc_w.l(printfa.o)
    0x08003214   0x08003214   0x00000024   Code   RO         2531    i._printf_post_padding  mc_w.l(printfa.o)
    0x08003238   0x08003238   0x0000002e   Code   RO         2532    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08003266   0x08003266   0x0000000a   Code   RO         2534    i._sputc            mc_w.l(printfa.o)
    0x08003270   0x08003270   0x0000001c   Code   RO         2023    i.delay_us          ultrasonic.o
    0x0800328c   0x0800328c   0x00000018   Code   RO          189    i.fputc             usart.o
    0x080032a4   0x080032a4   0x00000098   Code   RO          240    i.main              main.o
    0x0800333c   0x0800333c   0x000000d0   Data   RO          241    .constdata          main.o
    0x0800340c   0x0800340c   0x00000010   Data   RO          543    .constdata          stm32f1xx_hal_rcc.o
    0x0800341c   0x0800341c   0x00000098   Data   RO         1847    .constdata          info.o
    0x080034b4   0x080034b4   0x00000032   Data   RO         2090    .constdata          fan.o
    0x080034e6   0x080034e6   0x00000002   PAD
    0x080034e8   0x080034e8   0x00000087   Data   RO          242    .conststring        main.o
    0x0800356f   0x0800356f   0x00000001   PAD
    0x08003570   0x08003570   0x0000004d   Data   RO         1765    .conststring        gesture.o
    0x080035bd   0x080035bd   0x00000003   PAD
    0x080035c0   0x080035c0   0x00000020   Data   RO         2608    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080035e0, Size: 0x00000370, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080035e0   0x00000004   Data   RW            7    .data               system_stm32f1xx.o
    0x20000004   0x080035e4   0x00000020   Data   RW          243    .data               main.o
    0x20000024   0x08003604   0x00000004   Data   RW          815    .data               stm32f1xx_hal.o
    0x20000028   0x08003608   0x00000028   Data   RW         1766    .data               gesture.o
    0x20000050   0x08003630   0x00000008   Data   RW         1971    .data               dac.o
    0x20000058   0x08003638   0x00000004   Data   RW         2224    .data               photoswitch.o
    0x2000005c   0x0800363c   0x00000004   Data   RW         2582    .data               mc_w.l(stdout.o)
    0x20000060        -       0x00000040   Zero   RW          190    .bss                usart.o
    0x200000a0        -       0x0000001c   Zero   RW         1764    .bss                gesture.o
    0x200000bc        -       0x00000064   Zero   RW         1846    .bss                info.o
    0x20000120        -       0x00000014   Zero   RW         1970    .bss                dac.o
    0x20000134        -       0x00000020   Zero   RW         2089    .bss                fan.o
    0x20000154        -       0x0000001c   Zero   RW         2223    .bss                photoswitch.o
    0x20000170        -       0x00000200   Zero   RW          118    STACK               startup_stm32f103xe.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       164         28          0          8         20       1914   dac.o
       680         98         50          0         32      10487   fan.o
       332         24         77         40         28       5874   gesture.o
        28          6          0          0          0        791   gpio.o
       810        302        152          0        100       8497   info.o
      1142        440        343         32          0      52945   main.o
       372         54          0          4         28       5656   photoswitch.o
        36          8        304          0        512        800   startup_stm32f103xe.o
       120         16          0          4          0       3846   stm32f1xx_hal.o
       206         14          0          0          0      27036   stm32f1xx_hal_cortex.o
       218          0          0          0          0       4004   stm32f1xx_hal_dac.o
       508         42          0          0          0       3827   stm32f1xx_hal_gpio.o
        92          6          0          0          0        914   stm32f1xx_hal_msp.o
      2510         86         16          0          0       6456   stm32f1xx_hal_rcc.o
       668          4          0          0          0       4188   stm32f1xx_hal_uart.o
        14          0          0          0          0        498   stm32f1xx_it.o
        72         16          0          4          0     262997   system_stm32f1xx.o
       272         36          0          0          0       2844   ultrasonic.o
       152         24          0          0         64       2230   usart.o

    ----------------------------------------------------------------------
      8404       <USER>        <GROUP>         92        784     405804   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         8          0          6          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2308         96          0          0          0        604   printfa.o
         0          0          0          4          0          0   stdout.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o
        18          0          0          0          0         68   fflti.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      4408        <USER>          <GROUP>          4          0       2760   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2684        112          0          4          0       1224   mc_w.l
      1722          0          0          0          0       1536   mf_w.l

    ----------------------------------------------------------------------
      4408        <USER>          <GROUP>          4          0       2760   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12812       1316        980         96        784     402840   Grand Totals
     12812       1316        980         96        784     402840   ELF Image Totals
     12812       1316        980         96          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13792 (  13.47kB)
    Total RW  Size (RW Data + ZI Data)               880 (   0.86kB)
    Total ROM Size (Code + RO Data + RW Data)      13888 (  13.56kB)

==============================================================================

