#ifndef __DAC_H
#define __DAC_H
#include "stm32f1xx_hal.h"
#include "stm32f1xx_hal_dac.h"

//////////////////////////////////////////////////////////////////////////////////	 
// STM32F103VET6 DAC输出模块
// 使用DAC1_CH1输出模拟电压
// 引脚：PA4 (DAC1_CH1) - 与F407保持一致
//////////////////////////////////////////////////////////////////////////////////

// DAC相关定义
#define DAC_CHANNEL         DAC_CHANNEL_1
#define DAC_GPIO_PORT       GPIOA
#define DAC_GPIO_PIN        GPIO_PIN_4

// DAC参数
#define DAC_MAX_VALUE       4095    // 12位DAC最大值
#define DAC_MIN_VALUE       0       // DAC最小值
#define DAC_VREF            3.3f    // 参考电压(V)

// 全局变量声明
extern DAC_HandleTypeDef hdac;
extern DAC_ChannelConfTypeDef sConfig;

// 函数声明
void DAC_Init(void);                          // DAC初始化
void DAC_SetValue(uint16_t value);           // 设置DAC数值(0-4095)
void DAC_SetVoltage(float voltage);         // 设置DAC电压(0-3.3V)
uint16_t DAC_GetValue(void);                // 获取当前DAC数值
float DAC_GetVoltage(void);                 // 获取当前DAC电压

#endif
