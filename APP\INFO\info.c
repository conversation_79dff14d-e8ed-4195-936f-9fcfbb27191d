#include "info.h"
#include <stdio.h>
#include <string.h>
//#include "stm32f10x.h"
#include <stdarg.h>
#include <stddef.h>

# define STR_LENGTH 100
//////////////////////////////////////////////////////////////////////////////////
// 非接触式控制盘 - 串口屏信息发送模块实现 (STM32F103VET6版本)
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
char info_buffer[INFO_BUFFER_SIZE];

// 风扇电压-转速查找表（基于实际测量数据）
// 电压范围：3V-12V，每0.5V一个点
typedef struct {
    float voltage;      // 电压(V)
    uint16_t rpm;       // 转速(RPM)
} FanVoltageRpmMap_t;

const FanVoltageRpmMap_t fan_voltage_rpm_table[] = {
    {3.0f,  1490},      // 3V   -> 1490 RPM
    {3.5f,  1819},      // 3.5V -> 1819 RPM
    {4.0f,  2091},      // 4V   -> 2091 RPM
    {4.5f,  2388},      // 4.5V -> 2388 RPM
    {5.0f,  2704},      // 5V   -> 2704 RPM
    {5.5f,  2974},      // 5.5V -> 2974 RPM
    {6.0f,  3315},      // 6V   -> 3315 RPM
    {6.5f,  3592},      // 6.5V -> 3592 RPM
    {7.0f,  3948},      // 7V   -> 3948 RPM
    {7.5f,  4205},      // 7.5V -> 4205 RPM
    {8.0f,  4530},      // 8V   -> 4530 RPM
    {8.5f,  4796},      // 8.5V -> 4796 RPM
    {9.0f,  5100},      // 9V   -> 5100 RPM
    {9.5f,  5393},      // 9.5V -> 5393 RPM
    {10.0f, 5690},      // 10V  -> 5690 RPM
    {10.5f, 5978},      // 10.5V-> 5978 RPM
    {11.0f, 6328},      // 11V  -> 6328 RPM
    {11.5f, 6601},      // 11.5V-> 6601 RPM
    {12.0f, 6980}       // 12V  -> 6980 RPM
};

#define FAN_VOLTAGE_RPM_TABLE_SIZE  (sizeof(fan_voltage_rpm_table) / sizeof(FanVoltageRpmMap_t))

/**
 * @brief  信息模块初始化
 * @param  None
 * @retval None
 */
void Info_Init(void)
{
    // 清空缓冲区
    memset(info_buffer, 0, INFO_BUFFER_SIZE);

    // 发送初始化信息到串口屏
    Info_SendSetTime(0);
    Info_SendCountdown(0);
    Info_SendSetVoltage(0.0f);
    Info_SendDistance(0.0f);
    Info_SendRPM(0);
    Info_SendDirection(FAN_DIR_STOP);
    Info_SendMotionState(MOTION_STOP);
}

/**
 * @brief  根据电压查找对应的转速（使用查找表和线性插值）
 * @param  voltage: 电压值(V)
 * @retval 对应的转速(RPM)
 */
uint16_t Info_GetRpmByVoltage(float voltage)
{
    uint8_t i;
    float voltage_diff, rpm_diff, ratio;

    // 如果电压为0或小于最小值，返回0
    if(voltage <= 0.0f || voltage < fan_voltage_rpm_table[0].voltage)
        return 0;

    // 如果电压大于等于最大值，返回最大转速
    if(voltage >= fan_voltage_rpm_table[FAN_VOLTAGE_RPM_TABLE_SIZE - 1].voltage)
        return fan_voltage_rpm_table[FAN_VOLTAGE_RPM_TABLE_SIZE - 1].rpm;

    // 在查找表中查找合适的区间进行线性插值
    for(i = 0; i < FAN_VOLTAGE_RPM_TABLE_SIZE - 1; i++)
    {
        if(voltage >= fan_voltage_rpm_table[i].voltage &&
           voltage <= fan_voltage_rpm_table[i + 1].voltage)
        {
            // 找到区间，进行线性插值
            voltage_diff = fan_voltage_rpm_table[i + 1].voltage - fan_voltage_rpm_table[i].voltage;
            rpm_diff = fan_voltage_rpm_table[i + 1].rpm - fan_voltage_rpm_table[i].rpm;
            ratio = (voltage - fan_voltage_rpm_table[i].voltage) / voltage_diff;

            return fan_voltage_rpm_table[i].rpm + (uint16_t)(ratio * rpm_diff);
        }
    }

    // 默认返回0（不应该到达这里）
    return 0;
}



/**
 * @brief  发送命令到串口屏
 * @param  command: 要发送的命令字符串
 * @retval None
 */
void Info_SendCommand(const char* command)
{
    uint8_t end_cmd[3] = INFO_END_CMD;

    // 发送命令字符串
    HAL_UART_Transmit(&huart1, (uint8_t*)command, strlen(command), 1000);

    // 发送结束符
    HAL_UART_Transmit(&huart1, end_cmd, 3, 1000);
}

/**
 * @brief  发送设定运行时间
 * @param  time_seconds: 设定时间(秒)
 * @retval None
 */
void Info_SendSetTime(uint16_t time_seconds)
{
    sprintf(info_buffer, "t0_set_time.txt=\"%ds\"", time_seconds);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送倒计时
 * @param  countdown_seconds: 倒计时(秒)
 * @retval None
 */
void Info_SendCountdown(uint16_t countdown_seconds)
{
    sprintf(info_buffer, "t1_countdown.txt=\"%ds\"", countdown_seconds);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送设定工作电压
 * @param  voltage: 电压值(V)
 * @retval None
 */
void Info_SendSetVoltage(float voltage)
{
    sprintf(info_buffer, "t2_set_u.txt=\"%.1fV\"", voltage);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送操作距离
 * @param  distance: 距离值(cm)
 * @retval None
 */
void Info_SendDistance(float distance)
{
    sprintf(info_buffer, "t3_cmd_d.txt=\"%.1fcm\"", distance);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送风扇转速
 * @param  rpm: 转速(RPM)
 * @retval None
 */
void Info_SendRPM(uint16_t rpm)
{
    sprintf(info_buffer, "t4_rpm.txt=\"%dRPM\"", rpm);
    Info_SendCommand(info_buffer);
}

void TJCPrintf(const char *str, ...)
{


	uint8_t end[3] ={0xff,0xff,0xff};
	char buffer[STR_LENGTH+1];  // 数据长度
	va_list arg_ptr;
	va_start(arg_ptr, str);
	int len = vsnprintf(buffer, STR_LENGTH+1, str, arg_ptr);
	va_end(arg_ptr);
	HAL_UART_Transmit(&huart1,buffer,len,100);
	HAL_UART_Transmit(&huart1,end,3,100);
	
	
	/*for(int i = 0; i < len; i++)
	{
		USART_SendData(USART1, buffer[i]);
		while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	//等待发送完毕
	}

	USART_SendData(USART1, end);			//这个函数改为你的单片机的串口发送单字节函数
	while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	//等待发送完毕
	USART_SendData(USART1, end);			//这个函数改为你的单片机的串口发送单字节函数
	while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	//等待发送完毕
	USART_SendData(USART1, end);			//这个函数改为你的单片机的串口发送单字节函数
	while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	//等待发送完毕
*/
}

/**
 * @brief  发送风扇转向
 * @param  direction: 风扇转向
 * @retval None
 */
void Info_SendDirection(InfoFanDirection_t direction)
{
    const char* dir_str = "";

    switch(direction)
    {
        case FAN_DIR_FORWARD:
            dir_str = " 正转 ";
			break;
        case FAN_DIR_REVERSE:
            dir_str = " 反转 ";
            break;
        case FAN_DIR_STOP:
        default:
            dir_str = " 停止 ";		
            break;
    }
	sprintf(info_buffer, "t5_direction.txt=\"%s\"",dir_str);  // 显示停止图片
	
   Info_SendCommand(info_buffer);
}

/**
 * @brief  发送运动状态
 * @param  state: 运动状态
 * @retval None
 */
void Info_SendMotionState(InfoMotionState_t state)
{
    if(state == MOTION_RUNNING)
    {
        sprintf(info_buffer, "vis 9,1");  // 显示运行图片
    }
    else
    {
        sprintf(info_buffer, "vis 9,0");  // 显示停止图片
    }

    Info_SendCommand(info_buffer);
}

/**
 * @brief  更新最终测定的距离给串口屏
 * @param  FinalDistance: 最终距离值
 * @retval None
 */
void Info_SendFinalDistance(int FinalDistance)
{
    sprintf(info_buffer, "t6_final_d.txt=\"最终距离为:%dcm\"", FinalDistance);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  更新所有信息 - 批量发送当前状态
 * @param  None
 * @retval None
 */
void Info_UpdateAll(void)
{
    // 这个函数可以在需要时批量更新所有信息
    // 具体的数值需要从其他模块获取
    // 这里提供一个框架，实际使用时传入具体参数
}

/**
 * @brief  测试串口屏通信
 * @param  None
 * @retval None
 */
void Info_Test(void)
{
    // 发送测试信息
    Info_SendSetTime(30);
    Info_SendCountdown(25);
    Info_SendSetVoltage(5.5f);
    Info_SendDistance(12.3f);
    Info_SendRPM(1500);
    Info_SendDirection(FAN_DIR_FORWARD);
    Info_SendMotionState(MOTION_RUNNING);
}

/**
 * @brief  根据系统状态发送时间相关信息
 * @param  set_time: 设定时间(秒)
 * @param  countdown: 倒计时(秒)
 * @retval None
 */
void Info_SendTimeInfo(uint16_t set_time, uint16_t countdown)
{
    Info_SendSetTime(set_time);
    Info_SendCountdown(countdown);
}

/**
 * @brief  根据风扇状态发送完整风扇信息（使用查找表）
 * @param  is_running: 风扇是否运行
 * @param  voltage: 风扇实际电压(V)
 * @param  direction: 风扇转向
 * @retval None
 */
void Info_SendFanInfoWithLookup(uint8_t is_running, float voltage, InfoFanDirection_t direction)
{
    uint16_t actual_rpm = 0;

    // 发送实际电压
    Info_SendSetVoltage(voltage);

    // 使用查找表计算实际转速
    if(is_running && voltage > 0.0f) {
        actual_rpm = Info_GetRpmByVoltage(voltage);
    } else {
        actual_rpm = 0;
    }
    Info_SendRPM(actual_rpm);

    // 发送转向和运动状态
    Info_SendDirection(direction);
    if(is_running) {
        Info_SendMotionState(MOTION_RUNNING);
    } else {
        Info_SendMotionState(MOTION_STOP);
    }
}

/**
 * @brief  根据风扇状态发送完整风扇信息（兼容旧版本）
 * @param  is_running: 风扇是否运行
 * @param  voltage: 风扇电压(V)
 * @param  pwm_duty: PWM占空比(0-1000)
 * @retval None
 */
void Info_SendFanInfo(uint8_t is_running, float voltage, uint16_t pwm_duty)
{
    uint16_t estimated_rpm = 0;
    // 发送电压
    Info_SendSetVoltage(voltage);

    // 发送转速(根据PWM占空比估算)
    estimated_rpm = is_running ? (pwm_duty * 30) : 0;  // 简单估算公式
    Info_SendRPM(estimated_rpm);

    // 发送转向和运动状态
    if(is_running) {
        Info_SendDirection(FAN_DIR_FORWARD);  // 简化为正转
        Info_SendMotionState(MOTION_RUNNING);
    } else {
        Info_SendDirection(FAN_DIR_STOP);
        Info_SendMotionState(MOTION_STOP);
    }
}
