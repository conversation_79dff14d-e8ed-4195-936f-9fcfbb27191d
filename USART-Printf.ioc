#MicroXplorer Configuration settings - do not modify
File.Version=6
KeepUserPlacement=true
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SYS
Mcu.IP3=USART1
Mcu.IPNb=4
Mcu.Name=STM32F103V(C-D-E)Tx
Mcu.Package=LQFP100
Mcu.Pin0=OSC_IN
Mcu.Pin1=OSC_OUT
Mcu.Pin2=PA9
Mcu.Pin3=PA10
Mcu.Pin4=VP_SYS_VS_Systick
Mcu.PinsNb=5
Mcu.UserConstants=
Mcu.UserName=STM32F103VETx
MxCube.Version=4.14.0
MxDb.Version=DB.4.0.140
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:false
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:false
OSC_IN.Mode=HSE-External-Oscillator
OSC_IN.Signal=RCC_OSC_IN
OSC_OUT.Mode=HSE-External-Oscillator
OSC_OUT.Signal=RCC_OSC_OUT
PA10.GPIOParameters=GPIO_PuPd
PA10.GPIO_PuPd=GPIO_NOPULL
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PCC.Checker=false
PCC.Line=STM32F103
PCC.MCU=STM32F103V(C-D-E)Tx
PCC.MXVersion=4.14.0
PCC.PartNumber=STM32F103VETx
PCC.Seq0=0
PCC.Series=STM32F1
PCC.Temperature=25
PCC.Vdd=3.3
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=2
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.3.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x400
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=USART-Printf.ioc
ProjectManager.ProjectName=USART-Printf
ProjectManager.StackSize=0x200
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false,2-MX_USART1_UART_Init-USART1-false
RCC.APB1Freq_Value=8000000
RCC.APB2Freq_Value=8000000
RCC.FamilyName=M
RCC.IPParameters=FamilyName,PLLCLKFreq_Value,PLLMCOFreq_Value,TimSysFreq_Value,APB1Freq_Value,APB2Freq_Value
RCC.PLLCLKFreq_Value=8000000
RCC.PLLMCOFreq_Value=4000000
RCC.TimSysFreq_Value=8000000
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=USART-Printf
