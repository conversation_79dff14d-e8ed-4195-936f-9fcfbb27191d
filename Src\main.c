/************************************************
 STM32F103VET6 非接触式手势识别风扇控制系统
 移植自STM32F407ZGT6平台

 【系统功能概述】
 本系统实现了基于手势识别的非接触式风扇控制，主要功能包括：
 1. 手势识别：通过4个光电开关检测挥手动作，识别8种手势组合
 2. 距离测量：超声波传感器测量5-30cm范围内的距离
 3. 风扇控制：DAC调速、正反转控制、启停控制
 4. 智能设定：基于距离的时间和电压设定功能

 【手势识别原理】
 - S1→S2: 依次触发光电开关S1和S2，实现风扇正转启动
 - S2→S1: 依次触发光电开关S2和S1，实现风扇正转停止
 - S3→S4: 依次触发光电开关S3和S4，实现风扇反转启动
 - S4→S3: 依次触发光电开关S4和S3，实现风扇反转停止
 - S4→S2: 运行时电压上升手势
 - S3→S1: 运行时电压下降手势
 - S3→S2: 距离5-20cm时的时间设定手势
 - S4→S1: 距离5-20cm时的电压设定手势

 【硬件连接 - STM32F103VET6版本】
 - 光电开关S1-S4: PC0-PC3 (低电平触发)
 - 超声波测距S5: PB6(TRIG), PB7(ECHO) (HC-SR04)
 - 风扇速度控制: PA4 (DAC1_CH1, 模拟电压输出0-3.3V)
 - 风扇方向控制: PA6 (数字IO，高电平=正转，低电平=反转)
 - 风扇使能控制: PA5 (数字IO，可选)

************************************************/

/* Includes ------------------------------------------------------------------*/
#include "stm32f1xx_hal.h"
#include "usart.h"
#include "gpio.h"

/* USER CODE BEGIN Includes */
// 硬件模块
#include "../HARDWARE/ULTRASONIC/ultrasonic.h"     // 超声波测距
#include "../HARDWARE/PHOTOSWITCH/photoswitch.h"   // 光电门检测
#include "../HARDWARE/FAN/fan.h"                   // 风扇控制
#include "../HARDWARE/DAC/dac.h"                   // DAC控制

// 应用模块
#include "../APP/GESTURE/gesture.h"                // 手势识别
#include "../APP/INFO/info.h"                      // 串口屏信息发送

/* USER CODE END Includes */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
/* Private variables ---------------------------------------------------------*/

// 全局变量
float distance = 0;
GestureType_t current_gesture = GESTURE_NONE;
float dac_voltage = 0;  // DAC输出电压

// 串口屏显示相关变量
uint32_t last_display_update = 0;
#define DISPLAY_UPDATE_INTERVAL 100  // 串口屏更新间隔100ms
float actual_voltage = 0;
InfoFanDirection_t info_direction = FAN_DIR_STOP;

// 距离稳定性检测相关变量
#define STABILITY_TIME_MS       2000    // 稳定时间要求：2秒
uint16_t current_rounded_distance = 0;  // 当前四舍五入后的距离
uint16_t last_stable_distance = 0;      // 上次稳定的距离值
uint32_t stability_start_time = 0;      // 稳定性检测开始时间
uint8_t is_distance_stable = 0;         // 距离是否稳定标志
uint16_t final_d = 0;                   // 最终确定的距离值
uint16_t final_d_ready = 0;				// 是否有新的与距离相关命令标志位

// 距离到时间查找表 [距离cm][时间秒]
#define DISTANCE_TIME_TABLE_SIZE    26  // 5-30cm，共26个数据点
const uint16_t distance_time_table[DISTANCE_TIME_TABLE_SIZE][2] = {
    {5,  10},   {6,  12},   {7,  14},   {8,  16},   {9,  18},   // 5-9cm
    {10, 20},   {11, 22},   {12, 24},   {13, 26},   {14, 28},   // 10-14cm
    {15, 30},   {16, 32},   {17, 34},   {18, 36},   {19, 38},   // 15-19cm
    {20, 40},   {21, 42},   {22, 44},   {23, 46},   {24, 48},   // 20-24cm
    {25, 50},   {26, 52},   {27, 54},   {28, 56},   {29, 58},   // 25-29cm
    {30, 60}                                                     // 30cm
};

// 距离到电压查找表 [距离cm][电压*10(便于存储)]
#define DISTANCE_VOLTAGE_TABLE_SIZE 26  // 5-30cm，共26个数据点
const uint16_t distance_voltage_table[DISTANCE_VOLTAGE_TABLE_SIZE][2] = {
    {5,  30},   {6,  34},   {7,  38},   {8,  42},   {9,  46},   // 5-9cm -> 3.0-4.6V
    {10, 50},   {11, 54},   {12, 58},   {13, 62},   {14, 66},   // 10-14cm -> 5.0-6.6V
    {15, 70},   {16, 74},   {17, 78},   {18, 82},   {19, 86},   // 15-19cm -> 7.0-8.6V
    {20, 90},   {21, 92},   {22, 94},   {23, 96},   {24, 98},   // 20-24cm -> 9.0-9.8V
    {25, 100},  {26, 100},  {27, 100},  {28, 100},  {29, 100},  // 25-29cm -> 10.0V
    {30, 100}                                                    // 30cm -> 10.0V
};

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/* USER CODE BEGIN PFP */
/* Private function prototypes -----------------------------------------------*/

// 系统初始化函数
void System_Init(void);

// 距离稳定性检测函数
void Check_Distance_Stability(void);

// 查找表函数
uint16_t Get_Time_From_Distance(uint16_t distance);
uint16_t Get_Voltage_From_Distance(uint16_t distance);

// 手势处理函数
void Process_Gesture(GestureType_t gesture);

/* USER CODE END PFP */

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration----------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* Configure the system clock */
  SystemClock_Config();

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();

  /* USER CODE BEGIN 2 */

  // 系统初始化
  System_Init();

  /* USER CODE END 2 */
/*****************************************************************************/

  // 测试区域

//  current_gesture = GESTURE_S1_TO_S2;
//  Process_Gesture(current_gesture);
//  Fan_Update();
//  HAL_Delay(200);
//  current_gesture = GESTURE_S4_TO_S2;
//  Process_Gesture(current_gesture);
//  Fan_Update();


/******************************************************************************/
  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
  /* USER CODE END WHILE */

  /* USER CODE BEGIN 3 */

    // 1. 更新光电开关状态
    PhotoSwitch_Update();

    // 2. 手势识别
    current_gesture = Gesture_Recognize();
    if(current_gesture != GESTURE_NONE)
    {
        Process_Gesture(current_gesture);
    }
	
    // 3. 距离测量和稳定性检测
    distance = Ultrasonic_GetDistance();
    Check_Distance_Stability();

    // 4. 更新风扇状态
    Fan_Update();

    // 5. 串口屏信息更新（每100ms更新一次）
    uint32_t current_time = HAL_GetTick();
    if(current_time - last_display_update >= DISPLAY_UPDATE_INTERVAL)
    {
		
        // 发送距离信息
        Info_SendDistance(distance);

        // 发送风扇信息（使用查找表获取实际电压和转速）
        actual_voltage = Fan_GetVoltage() / 1000.0f;  // 转换为V

        // 转换风扇方向枚举
        if(Fan_IsRunning()) {
            info_direction = (Fan_GetDirection() == FAN_FORWARD) ? FAN_DIR_FORWARD : FAN_DIR_REVERSE;
        } else {
            info_direction = FAN_DIR_STOP;
        }

        // 使用新的查找表函数发送风扇信息
        Info_SendFanInfoWithLookup(Fan_IsRunning(), actual_voltage, info_direction);

        // 发送最终距离信息（如果有稳定距离）
        if(is_distance_stable && final_d_ready)
        {
            Info_SendFinalDistance(final_d);
        }

        last_display_update = current_time;
    }

    // 6. 短暂延时
    HAL_Delay(10);

  }
  /* USER CODE END 3 */

}

/** System Clock Configuration
*/
void SystemClock_Config(void)
{

  RCC_OscInitTypeDef RCC_OscInitStruct;
  RCC_ClkInitTypeDef RCC_ClkInitStruct;

  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = 16;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  HAL_RCC_OscConfig(&RCC_OscInitStruct);

  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
  HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0);

  HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq()/1000);

  HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK);

  /* SysTick_IRQn interrupt configuration */
  HAL_NVIC_SetPriority(SysTick_IRQn, 0, 0);
}

/* USER CODE BEGIN 4 */

/**
 * @brief  系统初始化
 * @param  None
 * @retval None
 */
void System_Init(void)
{
    // 初始化硬件模块
    Ultrasonic_Init();      // 超声波测距模块
    PhotoSwitch_Init();     // 光电开关模块
    DAC_Init();             // DAC模块
    Fan_Init();             // 风扇控制模块

    // 初始化应用模块
    Gesture_Init();         // 手势识别模块
    Info_Init();            // 串口屏信息发送模块

    // 初始化全局变量
    distance = 0;
    current_gesture = GESTURE_NONE;
    dac_voltage = 0;

    // 初始化距离稳定性检测变量
    current_rounded_distance = 0;
    last_stable_distance = 0;
    stability_start_time = 0;
    is_distance_stable = 0;
    final_d = 0;
    final_d_ready = 0;
}

/**
 * @brief  距离稳定性检测
 * @param  None
 * @retval None
 */
void Check_Distance_Stability(void)
{
    uint32_t current_time = HAL_GetTick();

    // 四舍五入到整数厘米
    current_rounded_distance = (uint16_t)(distance + 0.5f);

    // 检查距离是否在有效范围内 (5-30cm)
    if(current_rounded_distance < 5 || current_rounded_distance > 30)
    {
        // 距离超出范围，重置稳定性检测
        is_distance_stable = 0;
        stability_start_time = 0;
        return;
    }

    // 检查距离是否发生变化
    if(current_rounded_distance != last_stable_distance)
    {
        // 距离发生变化，重新开始稳定性检测
        last_stable_distance = current_rounded_distance;
        stability_start_time = current_time;
        is_distance_stable = 0;
    }
    else
    {
        // 距离保持不变，检查是否已稳定足够时间
        if(!is_distance_stable &&
           (current_time - stability_start_time >= STABILITY_TIME_MS))
        {
            // 距离已稳定2秒，确定最终距离值
            is_distance_stable = 1;
            final_d = current_rounded_distance;
            final_d_ready = 1;  // 设置新距离就绪标志
        }
    }
}

/**
 * @brief  根据距离查找对应的时间值
 * @param  distance: 距离值(cm)
 * @retval 对应的时间值(秒)
 */
uint16_t Get_Time_From_Distance(uint16_t distance)
{
    uint8_t i;

    // 在查找表中搜索匹配的距离
    for(i = 0; i < DISTANCE_TIME_TABLE_SIZE; i++)
    {
        if(distance_time_table[i][0] == distance)
        {
            return distance_time_table[i][1];
        }
    }

    // 如果没有找到精确匹配，返回默认值
    return 30;  // 默认30秒
}

/**
 * @brief  根据距离查找对应的电压值
 * @param  distance: 距离值(cm)
 * @retval 对应的电压值(V*10)
 */
uint16_t Get_Voltage_From_Distance(uint16_t distance)
{
    uint8_t i;

    // 在查找表中搜索匹配的距离
    for(i = 0; i < DISTANCE_VOLTAGE_TABLE_SIZE; i++)
    {
        if(distance_voltage_table[i][0] == distance)
        {
            return distance_voltage_table[i][1];
        }
    }

    // 如果没有找到精确匹配，返回默认值
    return 50;  // 默认5.0V
}

/**
 * @brief  手势处理函数
 * @param  gesture: 识别到的手势类型
 * @retval None
 */
void Process_Gesture(GestureType_t gesture)
{
    uint16_t time_value, voltage_value;
    uint16_t voltage_mv;

    printf("检测到手势: %s\n\r", Gesture_GetName(gesture));

    switch(gesture)
    {
        case GESTURE_S1_TO_S2:  // 风扇正转启动
            printf("执行: 风扇正转启动\n\r");
            Fan_StartForward();
            break;

        case GESTURE_S2_TO_S1:  // 风扇正转停止
            printf("执行: 风扇正转停止\n\r");
            Fan_Stop();
            break;

        case GESTURE_S3_TO_S4:  // 风扇反转启动
            printf("执行: 风扇反转启动\n\r");
            Fan_StartReverse();
            break;

        case GESTURE_S4_TO_S3:  // 风扇反转停止
            printf("执行: 风扇反转停止\n\r");
            Fan_Stop();
            break;

        case GESTURE_S4_TO_S2:  // 电压上升（运行时）
            if(Fan_IsRunning())
            {
                printf("执行: 电压上升调节\n\r");
                Fan_StartVoltageAdjust(VOLTAGE_ADJUST_UP);
            }
            else
            {
                printf("风扇未运行，忽略电压调节手势\n\r");
            }
            break;

        case GESTURE_S3_TO_S1:  // 电压下降（运行时）
            if(Fan_IsRunning())
            {
                printf("执行: 电压下降调节\n\r");
                Fan_StartVoltageAdjust(VOLTAGE_ADJUST_DOWN);
            }
            else
            {
                printf("风扇未运行，忽略电压调节手势\n\r");
            }
            break;

        case GESTURE_S3_TO_S2:  // 时间设定（距离5-20cm时）
            if(is_distance_stable && final_d_ready && final_d >= 5 && final_d <= 20)
            {
                time_value = Get_Time_From_Distance(final_d);
                printf("执行: 时间设定 - 距离%dcm -> 时间%d秒\n\r", final_d, time_value);

                // 发送时间设定信息到串口屏
                Info_SendSetTime(time_value);
                Info_SendFinalDistance(final_d);

                final_d_ready = 0;  // 清除距离就绪标志
            }
            else
            {
                printf("距离不在5-20cm范围或未稳定，忽略时间设定手势\n\r");
            }
            break;

        case GESTURE_S4_TO_S1:  // 电压设定（距离5-20cm时）
            if(is_distance_stable && final_d_ready && final_d >= 5 && final_d <= 20)
            {
                voltage_value = Get_Voltage_From_Distance(final_d);
                voltage_mv = voltage_value * 100;  // 转换为mV
                printf("执行: 电压设定 - 距离%dcm -> 电压%d.%dV\n\r",
                       final_d, voltage_value/10, voltage_value%10);

                // 设置风扇电压
                Fan_SetSpeedByVoltage(voltage_mv);

                // 发送电压设定信息到串口屏
                Info_SendSetVoltage((float)voltage_value / 10.0f);
                Info_SendFinalDistance(final_d);

                final_d_ready = 0;  // 清除距离就绪标志
            }
            else
            {
                printf("距离不在5-20cm范围或未稳定，忽略电压设定手势\n\r");
            }
            break;

        default:
            printf("未知手势\n\r");
            break;
    }
}

/* USER CODE END 4 */

#ifdef USE_FULL_ASSERT

/**
   * @brief Reports the name of the source file and the source line number
   * where the assert_param error has occurred.
   * @param file: pointer to the source file name
   * @param line: assert_param error line source number
   * @retval None
   */
void assert_failed(uint8_t* file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
    ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */

}

#endif

/**
  * @}
  */ 

/**
  * @}
*/ 

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
